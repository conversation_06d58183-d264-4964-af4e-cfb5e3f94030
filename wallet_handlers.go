package main

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// DepositRequest 表示充值请求的结构
type DepositRequest struct {
	Description string         `json:"description,omitempty"`
	Metadata    map[string]any `json:"metadata,omitempty"`
	Amount      int            `json:"amount"`
}

// BalanceResponse 表示余额响应的结构
type BalanceResponse struct {
	Balance int `json:"balance"`
}

// DepositResponse 表示充值响应的结构
type DepositResponse struct {
	Balance int  `json:"balance"`
	Amount  int  `json:"amount"`
	Success bool `json:"success"`
}

// TransactionsResponse 表示交易记录响应的结构
type TransactionsResponse struct {
	Transactions []Transaction `json:"transactions"`
}

// HandleGetBalance 处理查询余额请求
// 添加钱包相关路由（两种模式都需要）
// @Summary 查询钱包余额
// @Description 获取当前用户的钱包余额信息
// @Tags 钱包
// @Security BearerAuth
// @Produce json
// @Success 200 {object} object "余额信息"
// @Failure 401 {object} SwaggerErrorResponse "未授权"
// @Router /api/v1/wallet/balance [get]
func HandleGetBalance(c *gin.Context) {
	// 只接受GET请求
	if c.Request.Method != http.MethodGet {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	// 确保钱包存在
	balance, err := services.DB.GetUserBalance(user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取余额失败"})
		return
	}
	// 返回余额
	resp := BalanceResponse{
		Balance: balance,
	}
	c.JSON(http.StatusOK, resp)
}

// TransactionsRequest
type TransactionsRequest struct {
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
}

// HandleGetTransactions 处理查询交易记录请求
// @Summary 查询交易记录
// @Description 获取当前用户的交易记录
// @Tags 钱包
// @Security BearerAuth
// @Produce json
// @Success 200 {object} object "交易记录"
// @Failure 401 {object} SwaggerErrorResponse "未授权"
// @Router /api/v1/wallet/transactions [get]
func HandleGetTransactions(c *gin.Context) {
	// 只接受POST请求
	if c.Request.Method != http.MethodPost {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	// 解析请求体
	var req TransactionsRequest = TransactionsRequest{}
	c.ShouldBindJSON(&req)
	// 默认值
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Offset < 0 {
		req.Offset = 0
	}
	// 获取交易记录
	transactions, err := services.DB.GetUserTransactionRecords(user.ID.String(), req.Limit, req.Offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get transactions"})
		return
	}
	// 返回交易记录
	resp := TransactionsResponse{
		Transactions: transactions,
	}
	c.JSON(http.StatusOK, resp)
}
