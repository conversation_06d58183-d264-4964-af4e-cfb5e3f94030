# OpenObserve 日志服务配置指南

## 概述

项目已从 Loki 日志服务迁移到 OpenObserve 日志服务。OpenObserve 提供更好的性能和更丰富的功能。

## 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# OpenObserve日志服务配置
LOG_SERVER_URL=http://192.168.192.43:5080
LOG_SERVER_TOKEN=****************************************************
```

### 配置说明

- `LOG_SERVER_URL`: OpenObserve 服务器地址
- `LOG_SERVER_TOKEN`: Base64 编码的认证令牌（格式：`username:password` 的 Base64 编码）

## 代码变更

### 主要变更

1. **依赖更新**: 添加了 `github.com/iamxvbaba/openobserve` 依赖
2. **Logger 结构**: 重构了 Logger 结构体，使用 OpenObserve 客户端
3. **线程安全**: 使用 `sync.Once` 确保 Logger 实例只初始化一次
4. **资源清理**: 添加了 `CleanupLogger()` 函数确保程序退出时正确清理资源

### 使用方法

日志接口保持不变，现有代码无需修改：

```go
// 基本日志方法
LogInfo("这是一条信息日志")
LogDebug("这是一条调试日志")
LogWarn("这是一条警告日志")
LogError("这是一条错误日志")

// 格式化日志
LogInfo("用户 %s 登录成功", userID)

// 兼容性方法
LogPrint("兼容 log.Print")
LogPrintf("兼容 log.Printf: %s", "参数")
LogPrintln("兼容 log.Println")
```

### 日志字段

每条日志包含以下字段：

- `_timestamp`: 时间戳（微秒）
- `level`: 日志级别（debug/info/warn/error）
- `job`: 任务名称（cloud-function-push）
- `app`: 应用名称（aig-function）
- `source`: 来源（serverless-aig）
- `log`: 日志消息内容

## 性能优化

### 特性

1. **批量发送**: 日志会批量发送到 OpenObserve 服务器
2. **压缩传输**: 启用 gzip 压缩减少网络传输
3. **异步处理**: 日志发送不会阻塞主程序执行
4. **缓冲机制**: 最多缓存 100 条日志后批量发送
5. **超时控制**: 30 秒等待时间，5 秒请求超时

### 配置参数

```go
openobserve.New(ctx, logServerURL,
    openobserve.WithFullSize(100),                    // 批量大小
    openobserve.WithCompress(true),                   // 启用压缩
    openobserve.WithWaitTime(time.Second*30),         // 等待时间
    openobserve.WithRequestTimeout(time.Second*5),    // 请求超时
    openobserve.WithIndexName("aig", true),           // 索引名称
    openobserve.WithAuthorization(logServerToken))   // 认证令牌
```

## 故障处理

### 降级机制

当 OpenObserve 服务不可用时，日志会自动降级到标准输出：

```go
if l.openObLog == nil {
    log.Printf("[%s] %s", level, message)
    return
}
```

### 资源清理

程序退出时会自动调用清理函数：

```go
defer CleanupLogger() // 在 main.go 中已添加
```

清理函数会：
1. 取消上下文
2. 等待 3 秒确保缓存的日志发送完成

## 监控和调试

### 查看日志

在 OpenObserve Web 界面中：
1. 访问 `http://192.168.192.43:5080`
2. 使用配置的用户名密码登录
3. 选择 `aig` 索引查看日志

### 常见问题

1. **日志不显示**: 检查 `LOG_SERVER_URL` 和 `LOG_SERVER_TOKEN` 配置
2. **连接超时**: 确认网络连接和服务器状态
3. **认证失败**: 验证 Token 是否正确编码

## 迁移完成

✅ 已完成的工作：
- 替换 Loki 为 OpenObserve
- 保持 API 兼容性
- 添加性能优化
- 实现故障降级
- 添加资源清理

现有代码无需修改，只需更新环境变量配置即可使用新的日志服务。
