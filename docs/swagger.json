{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "This is a serverless AI gateway API server with authentication, wallet management, and AI model integration.", "title": "Serverless AIG API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:9000", "basePath": "/", "paths": {"/api/v1/admin/finance/analysis": {"post": {"description": "管理员权限，按类别分析指定周期内的各项运营成本。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-财务管理"], "summary": "按分类获取支出分析", "parameters": [{"description": "分析周期", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.FinancialReportRequest"}}], "responses": {"200": {"description": "支出分析", "schema": {"type": "object"}}, "400": {"description": "无效的请求格式或缺少必要字段", "schema": {"type": "string"}}, "401": {"description": "未授权", "schema": {"type": "string"}}, "403": {"description": "权限不足", "schema": {"type": "string"}}, "500": {"description": "获取支出分析失败", "schema": {"type": "string"}}}}}, "/api/v1/admin/finance/expense": {"post": {"description": "管理员权限，用于录入一笔新的运营支出。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-财务管理"], "summary": "新增支出记录", "parameters": [{"description": "支出详情", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.AddExpenseRequest"}}], "responses": {"200": {"description": "支出记录创建成功", "schema": {"type": "object"}}, "400": {"description": "无效的请求格式或缺少必要字段", "schema": {"type": "string"}}, "401": {"description": "未授权", "schema": {"type": "string"}}, "403": {"description": "权限不足", "schema": {"type": "string"}}, "500": {"description": "创建支出记录失败", "schema": {"type": "string"}}}}}, "/api/v1/admin/finance/expenses": {"get": {"description": "管理员权限，分页列出所有已录入的支出记录。", "produces": ["application/json"], "tags": ["管理员-财务管理"], "summary": "列出支出记录", "parameters": [{"type": "integer", "description": "每页数量", "name": "limit", "in": "query"}, {"type": "integer", "description": "偏移量", "name": "offset", "in": "query"}], "responses": {"200": {"description": "支出列表", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"type": "string"}}, "403": {"description": "权限不足", "schema": {"type": "string"}}, "500": {"description": "获取支出列表失败", "schema": {"type": "string"}}}}}, "/api/v1/admin/finance/report": {"post": {"description": "管理员权限，生成包含营收、成本和利润的财务报表。", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-财务管理"], "summary": "获取综合财务报表", "parameters": [{"description": "报表周期", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.FinancialReportRequest"}}], "responses": {"200": {"description": "财务报告", "schema": {"type": "object"}}, "400": {"description": "无效的请求格式或缺少必要字段", "schema": {"type": "string"}}, "401": {"description": "未授权", "schema": {"type": "string"}}, "403": {"description": "权限不足", "schema": {"type": "string"}}, "500": {"description": "获取财务报表失败", "schema": {"type": "string"}}}}}, "/api/v1/admin/recharge/process": {"post": {"security": [{"BearerAuth": []}], "description": "管理员审批或拒绝充值申请,注意：充值申请表中的金额是人民币金额，而钱包中的余额是积分.换算关系为：1元人民币 = 1000积分", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-充值管理"], "summary": "处理充值申请", "responses": {"200": {"description": "处理结果", "schema": {"type": "object"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/admin/recharge/requests": {"get": {"security": [{"BearerAuth": []}], "description": "管理员获取所有用户的充值申请列表", "produces": ["application/json"], "tags": ["管理员-充值管理"], "summary": "获取充值申请列表", "responses": {"200": {"description": "充值申请列表", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/admin/user/balance/adjust": {"post": {"security": [{"BearerAuth": []}], "description": "管理员手动调整用户账户余额,注意：这里的余额调整是直接操作积分数量，不是人民币金额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-用户管理"], "summary": "调整用户余额", "responses": {"200": {"description": "调整结果", "schema": {"type": "object"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/admin/user/create": {"post": {"security": [{"BearerAuth": []}], "description": "管理员创建新的用户账户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-用户管理"], "summary": "创建用户账户", "responses": {"200": {"description": "创建结果", "schema": {"type": "object"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/admin/user/info": {"get": {"security": [{"BearerAuth": []}], "description": "管理员获取指定用户的详细信息", "produces": ["application/json"], "tags": ["管理员-用户管理"], "summary": "获取用户信息", "responses": {"200": {"description": "用户信息", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/admin/user/list": {"get": {"security": [{"BearerAuth": []}], "description": "管理员获取系统中所有用户的列表", "produces": ["application/json"], "tags": ["管理员-用户管理"], "summary": "获取用户列表", "responses": {"200": {"description": "用户列表", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "403": {"description": "权限不足", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/email-code": {"post": {"description": "向指定邮箱发送验证码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "发送邮箱验证码", "parameters": [{"description": "发送验证码请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerEmailCodeRequest"}}], "responses": {"200": {"description": "验证码发送成功", "schema": {"$ref": "#/definitions/main.SwaggerEmailCodeResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/email-login": {"post": {"description": "使用邮箱验证码登录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "邮箱验证码登录", "parameters": [{"description": "验证码登录请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerEmailLoginRequest"}}], "responses": {"200": {"description": "登录成功", "schema": {"$ref": "#/definitions/main.SwaggerLoginResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/login": {"post": {"description": "用户使用邮箱和密码登录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户登录", "parameters": [{"description": "登录请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerLoginRequest"}}], "responses": {"200": {"description": "登录成功", "schema": {"$ref": "#/definitions/main.SwaggerLoginResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "401": {"description": "认证失败", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/recover": {"post": {"description": "发送密码重置邮件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "密码恢复", "parameters": [{"description": "密码恢复请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerRecoverRequest"}}], "responses": {"200": {"description": "邮件发送成功", "schema": {"$ref": "#/definitions/main.SwaggerRecoverResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/refresh": {"post": {"description": "使用刷新令牌获取新的访问令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "刷新访问令牌", "responses": {"200": {"description": "刷新成功", "schema": {"$ref": "#/definitions/main.SwaggerLoginResponse"}}, "401": {"description": "刷新令牌无效", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/register": {"post": {"description": "用户使用邮箱和密码注册新账户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户注册", "parameters": [{"description": "注册请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerRegisterRequest"}}], "responses": {"200": {"description": "注册成功", "schema": {"$ref": "#/definitions/main.SwaggerRegisterResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/update-password": {"post": {"description": "更新用户密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "更新密码", "parameters": [{"description": "更新密码请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerUpdatePasswordRequest"}}], "responses": {"200": {"description": "密码更新成功", "schema": {"$ref": "#/definitions/main.SwaggerUpdatePasswordResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/auth/verify": {"post": {"description": "验证邮箱验证码或恢复令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "验证令牌", "parameters": [{"description": "验证请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/main.SwaggerVerifyRequest"}}], "responses": {"200": {"description": "验证成功", "schema": {"$ref": "#/definitions/main.SwaggerVerifyResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/protected": {"get": {"security": [{"BearerAuth": []}], "description": "测试认证是否正常工作的受保护端点", "produces": ["application/json"], "tags": ["认证"], "summary": "受保护的API测试", "responses": {"200": {"description": "认证成功", "schema": {"$ref": "#/definitions/main.SwaggerSuccessResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/recharge/list": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前用户的充值申请记录", "produces": ["application/json"], "tags": ["充值"], "summary": "查询充值申请列表", "responses": {"200": {"description": "充值申请列表", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/recharge/request": {"post": {"security": [{"BearerAuth": []}], "description": "用户提交充值申请", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["充值"], "summary": "提交充值申请", "responses": {"200": {"description": "申请提交成功", "schema": {"type": "object"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/tos/credentials": {"get": {"security": [{"BearerAuth": []}], "description": "获取访问腾讯云对象存储的临时凭证", "produces": ["application/json"], "tags": ["存储"], "summary": "获取TOS临时凭证", "responses": {"200": {"description": "临时凭证信息", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/version": {"get": {"description": "获取当前应用的版本信息", "produces": ["application/json"], "tags": ["系统"], "summary": "获取应用版本信息", "responses": {"200": {"description": "版本信息", "schema": {"$ref": "#/definitions/main.SwaggerAppVersionConfig"}}}}}, "/api/v1/wallet/balance": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前用户的钱包余额信息", "produces": ["application/json"], "tags": ["钱包"], "summary": "查询钱包余额", "responses": {"200": {"description": "余额信息", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v1/wallet/transactions": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前用户的交易记录", "produces": ["application/json"], "tags": ["钱包"], "summary": "查询交易记录", "responses": {"200": {"description": "交易记录", "schema": {"type": "object"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}, "/api/v2/chat/analysis": {"post": {"security": [{"BearerAuth": []}], "description": "使用AI模型分析图像内容", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AI服务"], "summary": "AI图像分析", "responses": {"200": {"description": "分析结果", "schema": {"type": "object"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/main.SwaggerErrorResponse"}}}}}}, "definitions": {"main.Action": {"type": "object", "properties": {"index": {"type": "integer"}, "selector": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "main.AddExpenseRequest": {"type": "object", "properties": {"amount": {"description": "支付金额，单位：分", "type": "integer"}, "category": {"description": "支出类别，必须是预定义的 expense_category 枚举值之一", "type": "string"}, "description": {"description": "支出描述，例如 \"阿里云ECS服务器 2核4G 一年\"", "type": "string"}, "invoice_url": {"description": "发票或凭证的URL链接", "type": "string"}, "payment_method": {"description": "支付方式，例如 \"alipay\", \"银行转账\"", "type": "string"}, "service_period_end": {"description": "本次支付覆盖的服务周期的结束日期，格式：YYYY-MM-DD", "type": "string"}, "service_period_start": {"description": "本次支付覆盖的服务周期的开始日期，格式：YYYY-MM-DD", "type": "string"}, "transaction_date": {"description": "实际支付日期，格式：YYYY-MM-DD", "type": "string"}, "vendor": {"description": "供应商/收款方，例如 \"阿里云\", \"GoDaddy\"", "type": "string"}}}, "main.ConfigItem": {"type": "object", "properties": {"actions": {"type": "array", "items": {"$ref": "#/definitions/main.Action"}}, "id": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}}}, "main.FinancialReportRequest": {"type": "object", "properties": {"end_date": {"description": "报表结束日期, 格式: YYYY-MM-DD", "type": "string"}, "start_date": {"description": "报表开始日期, 格式: YYYY-MM-DD", "type": "string"}}}, "main.Subject": {"type": "object", "properties": {"prompt_text": {"type": "string"}, "subject_id": {"type": "string"}, "subject_name": {"type": "string"}}}, "main.SwaggerAppVersionConfig": {"description": "应用版本信息", "type": "object", "properties": {"download_url": {"type": "string", "example": "https://example.com/download"}, "force_update": {"type": "boolean", "example": false}, "update_log": {"type": "string", "example": "修复了一些问题"}, "version": {"type": "string", "example": "v1.0.0"}}}, "main.SwaggerEmailCodeRequest": {"description": "发送邮箱验证码请求", "type": "object", "required": ["email"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}}}, "main.SwaggerEmailCodeResponse": {"description": "发送邮箱验证码响应", "type": "object", "properties": {"message": {"type": "string", "example": "验证码已发送到您的邮箱"}, "success": {"type": "boolean", "example": true}}}, "main.SwaggerEmailLoginRequest": {"description": "邮箱验证码登录请求", "type": "object", "required": ["code", "email"], "properties": {"code": {"type": "string", "example": "123456"}, "email": {"type": "string", "example": "<EMAIL>"}}}, "main.SwaggerErrorResponse": {"description": "错误响应", "type": "object", "properties": {"error": {"type": "string", "example": "Invalid request"}, "message": {"type": "string", "example": "请求参数错误"}}}, "main.SwaggerLoginRequest": {"description": "用户登录请求", "type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 6, "example": "password123"}}}, "main.SwaggerLoginResponse": {"description": "用户登录响应", "type": "object", "properties": {"access_token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "config": {"type": "array", "items": {"$ref": "#/definitions/main.ConfigItem"}}, "expires_at": {"type": "integer", "example": 1625097700}, "expires_in": {"type": "integer", "example": 3600}, "refresh_token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "subjects": {"type": "array", "items": {"$ref": "#/definitions/main.Subject"}}}}, "main.SwaggerRecoverRequest": {"description": "密码恢复请求", "type": "object", "required": ["email"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}}}, "main.SwaggerRecoverResponse": {"description": "密码恢复响应", "type": "object", "properties": {"message": {"type": "string", "example": "密码重置邮件已发送"}, "success": {"type": "boolean", "example": true}}}, "main.SwaggerRegisterRequest": {"description": "用户注册请求", "type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 6, "example": "password123"}}}, "main.SwaggerRegisterResponse": {"description": "用户注册响应", "type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "注册成功"}, "success": {"type": "boolean", "example": true}}}, "main.SwaggerSuccessResponse": {"description": "成功响应", "type": "object", "properties": {"message": {"type": "string", "example": "操作成功"}, "user": {}}}, "main.SwaggerUpdatePasswordRequest": {"description": "更新密码请求", "type": "object", "required": ["password"], "properties": {"password": {"type": "string", "minLength": 6, "example": "newpassword123"}}}, "main.SwaggerUpdatePasswordResponse": {"description": "更新密码响应", "type": "object", "properties": {"message": {"type": "string", "example": "密码更新成功"}, "success": {"type": "boolean", "example": true}}}, "main.SwaggerVerifyRequest": {"description": "验证令牌请求", "type": "object", "required": ["email", "token", "type"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "token": {"type": "string", "example": "123456"}, "type": {"type": "string", "example": "recovery"}}}, "main.SwaggerVerifyResponse": {"description": "验证令牌响应", "type": "object", "properties": {"access_token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "expires_at": {"type": "integer", "example": 1625097700}, "expires_in": {"type": "integer", "example": 3600}, "message": {"type": "string", "example": "验证成功"}, "refresh_token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "success": {"type": "boolean", "example": true}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-Access-Token", "in": "header"}}}