basePath: /
definitions:
  main.Action:
    properties:
      index:
        type: integer
      selector:
        type: string
      type:
        type: string
      value:
        type: string
    type: object
  main.AddExpenseRequest:
    properties:
      amount:
        description: 支付金额，单位：分
        type: integer
      category:
        description: 支出类别，必须是预定义的 expense_category 枚举值之一
        type: string
      description:
        description: 支出描述，例如 "阿里云ECS服务器 2核4G 一年"
        type: string
      invoice_url:
        description: 发票或凭证的URL链接
        type: string
      payment_method:
        description: 支付方式，例如 "alipay", "银行转账"
        type: string
      service_period_end:
        description: 本次支付覆盖的服务周期的结束日期，格式：YYYY-MM-DD
        type: string
      service_period_start:
        description: 本次支付覆盖的服务周期的开始日期，格式：YYYY-MM-DD
        type: string
      transaction_date:
        description: 实际支付日期，格式：YYYY-MM-DD
        type: string
      vendor:
        description: 供应商/收款方，例如 "阿里云", "GoDaddy"
        type: string
    type: object
  main.ConfigItem:
    properties:
      actions:
        items:
          $ref: '#/definitions/main.Action'
        type: array
      id:
        type: string
      name:
        type: string
      url:
        type: string
    type: object
  main.FinancialReportRequest:
    properties:
      end_date:
        description: '报表结束日期, 格式: YYYY-MM-DD'
        type: string
      start_date:
        description: '报表开始日期, 格式: YYYY-MM-DD'
        type: string
    type: object
  main.Subject:
    properties:
      prompt_text:
        type: string
      subject_id:
        type: string
      subject_name:
        type: string
    type: object
  main.SwaggerAppVersionConfig:
    description: 应用版本信息
    properties:
      download_url:
        example: https://example.com/download
        type: string
      force_update:
        example: false
        type: boolean
      update_log:
        example: 修复了一些问题
        type: string
      version:
        example: v1.0.0
        type: string
    type: object
  main.SwaggerEmailCodeRequest:
    description: 发送邮箱验证码请求
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  main.SwaggerEmailCodeResponse:
    description: 发送邮箱验证码响应
    properties:
      message:
        example: 验证码已发送到您的邮箱
        type: string
      success:
        example: true
        type: boolean
    type: object
  main.SwaggerEmailLoginRequest:
    description: 邮箱验证码登录请求
    properties:
      code:
        example: "123456"
        type: string
      email:
        example: <EMAIL>
        type: string
    required:
    - code
    - email
    type: object
  main.SwaggerErrorResponse:
    description: 错误响应
    properties:
      error:
        example: Invalid request
        type: string
      message:
        example: 请求参数错误
        type: string
    type: object
  main.SwaggerLoginRequest:
    description: 用户登录请求
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: password123
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  main.SwaggerLoginResponse:
    description: 用户登录响应
    properties:
      access_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      config:
        items:
          $ref: '#/definitions/main.ConfigItem'
        type: array
      expires_at:
        example: 1625097700
        type: integer
      expires_in:
        example: 3600
        type: integer
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      subjects:
        items:
          $ref: '#/definitions/main.Subject'
        type: array
    type: object
  main.SwaggerRecoverRequest:
    description: 密码恢复请求
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  main.SwaggerRecoverResponse:
    description: 密码恢复响应
    properties:
      message:
        example: 密码重置邮件已发送
        type: string
      success:
        example: true
        type: boolean
    type: object
  main.SwaggerRegisterRequest:
    description: 用户注册请求
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: password123
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  main.SwaggerRegisterResponse:
    description: 用户注册响应
    properties:
      code:
        example: 200
        type: integer
      message:
        example: 注册成功
        type: string
      success:
        example: true
        type: boolean
    type: object
  main.SwaggerSuccessResponse:
    description: 成功响应
    properties:
      message:
        example: 操作成功
        type: string
      user: {}
    type: object
  main.SwaggerUpdatePasswordRequest:
    description: 更新密码请求
    properties:
      password:
        example: newpassword123
        minLength: 6
        type: string
    required:
    - password
    type: object
  main.SwaggerUpdatePasswordResponse:
    description: 更新密码响应
    properties:
      message:
        example: 密码更新成功
        type: string
      success:
        example: true
        type: boolean
    type: object
  main.SwaggerVerifyRequest:
    description: 验证令牌请求
    properties:
      email:
        example: <EMAIL>
        type: string
      token:
        example: "123456"
        type: string
      type:
        example: recovery
        type: string
    required:
    - email
    - token
    - type
    type: object
  main.SwaggerVerifyResponse:
    description: 验证令牌响应
    properties:
      access_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      expires_at:
        example: 1625097700
        type: integer
      expires_in:
        example: 3600
        type: integer
      message:
        example: 验证成功
        type: string
      refresh_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      success:
        example: true
        type: boolean
    type: object
host: localhost:9000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a serverless AI gateway API server with authentication, wallet
    management, and AI model integration.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Serverless AIG API
  version: "1.0"
paths:
  /api/v1/admin/finance/analysis:
    post:
      consumes:
      - application/json
      description: 管理员权限，按类别分析指定周期内的各项运营成本。
      parameters:
      - description: 分析周期
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/main.FinancialReportRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 支出分析
          schema:
            type: object
        "400":
          description: 无效的请求格式或缺少必要字段
          schema:
            type: string
        "401":
          description: 未授权
          schema:
            type: string
        "403":
          description: 权限不足
          schema:
            type: string
        "500":
          description: 获取支出分析失败
          schema:
            type: string
      summary: 按分类获取支出分析
      tags:
      - 管理员-财务管理
  /api/v1/admin/finance/expense:
    post:
      consumes:
      - application/json
      description: 管理员权限，用于录入一笔新的运营支出。
      parameters:
      - description: 支出详情
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/main.AddExpenseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 支出记录创建成功
          schema:
            type: object
        "400":
          description: 无效的请求格式或缺少必要字段
          schema:
            type: string
        "401":
          description: 未授权
          schema:
            type: string
        "403":
          description: 权限不足
          schema:
            type: string
        "500":
          description: 创建支出记录失败
          schema:
            type: string
      summary: 新增支出记录
      tags:
      - 管理员-财务管理
  /api/v1/admin/finance/expenses:
    get:
      description: 管理员权限，分页列出所有已录入的支出记录。
      parameters:
      - description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 偏移量
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 支出列表
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            type: string
        "403":
          description: 权限不足
          schema:
            type: string
        "500":
          description: 获取支出列表失败
          schema:
            type: string
      summary: 列出支出记录
      tags:
      - 管理员-财务管理
  /api/v1/admin/finance/report:
    post:
      consumes:
      - application/json
      description: 管理员权限，生成包含营收、成本和利润的财务报表。
      parameters:
      - description: 报表周期
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/main.FinancialReportRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 财务报告
          schema:
            type: object
        "400":
          description: 无效的请求格式或缺少必要字段
          schema:
            type: string
        "401":
          description: 未授权
          schema:
            type: string
        "403":
          description: 权限不足
          schema:
            type: string
        "500":
          description: 获取财务报表失败
          schema:
            type: string
      summary: 获取综合财务报表
      tags:
      - 管理员-财务管理
  /api/v1/admin/recharge/process:
    post:
      consumes:
      - application/json
      description: 管理员审批或拒绝充值申请,注意：充值申请表中的金额是人民币金额，而钱包中的余额是积分.换算关系为：1元人民币 = 1000积分
      produces:
      - application/json
      responses:
        "200":
          description: 处理结果
          schema:
            type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 处理充值申请
      tags:
      - 管理员-充值管理
  /api/v1/admin/recharge/requests:
    get:
      description: 管理员获取所有用户的充值申请列表
      produces:
      - application/json
      responses:
        "200":
          description: 充值申请列表
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取充值申请列表
      tags:
      - 管理员-充值管理
  /api/v1/admin/user/balance/adjust:
    post:
      consumes:
      - application/json
      description: 管理员手动调整用户账户余额,注意：这里的余额调整是直接操作积分数量，不是人民币金额
      produces:
      - application/json
      responses:
        "200":
          description: 调整结果
          schema:
            type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 调整用户余额
      tags:
      - 管理员-用户管理
  /api/v1/admin/user/create:
    post:
      consumes:
      - application/json
      description: 管理员创建新的用户账户
      produces:
      - application/json
      responses:
        "200":
          description: 创建结果
          schema:
            type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 创建用户账户
      tags:
      - 管理员-用户管理
  /api/v1/admin/user/info:
    get:
      description: 管理员获取指定用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 用户信息
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取用户信息
      tags:
      - 管理员-用户管理
  /api/v1/admin/user/list:
    get:
      description: 管理员获取系统中所有用户的列表
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取用户列表
      tags:
      - 管理员-用户管理
  /api/v1/auth/email-code:
    post:
      consumes:
      - application/json
      description: 向指定邮箱发送验证码
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerEmailCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 验证码发送成功
          schema:
            $ref: '#/definitions/main.SwaggerEmailCodeResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 发送邮箱验证码
      tags:
      - 认证
  /api/v1/auth/email-login:
    post:
      consumes:
      - application/json
      description: 使用邮箱验证码登录
      parameters:
      - description: 验证码登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerEmailLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/main.SwaggerLoginResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 邮箱验证码登录
      tags:
      - 认证
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: 用户使用邮箱和密码登录
      parameters:
      - description: 登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/main.SwaggerLoginResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 用户登录
      tags:
      - 认证
  /api/v1/auth/recover:
    post:
      consumes:
      - application/json
      description: 发送密码重置邮件
      parameters:
      - description: 密码恢复请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerRecoverRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 邮件发送成功
          schema:
            $ref: '#/definitions/main.SwaggerRecoverResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 密码恢复
      tags:
      - 认证
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: 使用刷新令牌获取新的访问令牌
      produces:
      - application/json
      responses:
        "200":
          description: 刷新成功
          schema:
            $ref: '#/definitions/main.SwaggerLoginResponse'
        "401":
          description: 刷新令牌无效
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 刷新访问令牌
      tags:
      - 认证
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: 用户使用邮箱和密码注册新账户
      parameters:
      - description: 注册请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerRegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            $ref: '#/definitions/main.SwaggerRegisterResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 用户注册
      tags:
      - 认证
  /api/v1/auth/update-password:
    post:
      consumes:
      - application/json
      description: 更新用户密码
      parameters:
      - description: 更新密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerUpdatePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 密码更新成功
          schema:
            $ref: '#/definitions/main.SwaggerUpdatePasswordResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 更新密码
      tags:
      - 认证
  /api/v1/auth/verify:
    post:
      consumes:
      - application/json
      description: 验证邮箱验证码或恢复令牌
      parameters:
      - description: 验证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/main.SwaggerVerifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 验证成功
          schema:
            $ref: '#/definitions/main.SwaggerVerifyResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      summary: 验证令牌
      tags:
      - 认证
  /api/v1/protected:
    get:
      description: 测试认证是否正常工作的受保护端点
      produces:
      - application/json
      responses:
        "200":
          description: 认证成功
          schema:
            $ref: '#/definitions/main.SwaggerSuccessResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 受保护的API测试
      tags:
      - 认证
  /api/v1/recharge/list:
    get:
      description: 获取当前用户的充值申请记录
      produces:
      - application/json
      responses:
        "200":
          description: 充值申请列表
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 查询充值申请列表
      tags:
      - 充值
  /api/v1/recharge/request:
    post:
      consumes:
      - application/json
      description: 用户提交充值申请
      produces:
      - application/json
      responses:
        "200":
          description: 申请提交成功
          schema:
            type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 提交充值申请
      tags:
      - 充值
  /api/v1/tos/credentials:
    get:
      description: 获取访问腾讯云对象存储的临时凭证
      produces:
      - application/json
      responses:
        "200":
          description: 临时凭证信息
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取TOS临时凭证
      tags:
      - 存储
  /api/v1/version:
    get:
      description: 获取当前应用的版本信息
      produces:
      - application/json
      responses:
        "200":
          description: 版本信息
          schema:
            $ref: '#/definitions/main.SwaggerAppVersionConfig'
      summary: 获取应用版本信息
      tags:
      - 系统
  /api/v1/wallet/balance:
    get:
      description: 获取当前用户的钱包余额信息
      produces:
      - application/json
      responses:
        "200":
          description: 余额信息
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 查询钱包余额
      tags:
      - 钱包
  /api/v1/wallet/transactions:
    get:
      description: 获取当前用户的交易记录
      produces:
      - application/json
      responses:
        "200":
          description: 交易记录
          schema:
            type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: 查询交易记录
      tags:
      - 钱包
  /api/v2/chat/analysis:
    post:
      consumes:
      - application/json
      description: 使用AI模型分析图像内容
      produces:
      - application/json
      responses:
        "200":
          description: 分析结果
          schema:
            type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/main.SwaggerErrorResponse'
      security:
      - BearerAuth: []
      summary: AI图像分析
      tags:
      - AI服务
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: X-Access-Token
    type: apiKey
swagger: "2.0"
