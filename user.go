package main

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/supabase-community/auth-go/types"
)

// AdminUserListResponse 定义了管理员获取用户列表的响应结构
type AdminUserListResponse struct {
	Users   []User `json:"users"`
	Count   int    `json:"count"`
	Total   int    `json:"total"` // 总用户数，用于分页
	Success bool   `json:"success"`
}

// HandleAdminGetUserList 处理管理员获取用户列表的请求
// @Summary 获取用户列表
// @Description 管理员获取系统中所有用户的列表
// @Tags 管理员-用户管理
// @Security BearerAuth
// @Produce json
// @Success 200 {object} object "用户列表"
// @Failure 401 {object} SwaggerErrorResponse "未授权"
// @Failure 403 {object} SwaggerErrorResponse "权限不足"
// @Router /api/v1/admin/user/list [get]
func (a *AuthClient) HandleAdminGetUserList(c *gin.Context) {
	// 只接受GET请求
	if c.Request.Method != http.MethodGet {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 从上下文中获取用户信息
	requestUser, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	// 检查管理员权限
	if !isAdmin(requestUser.ID.String()) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}
	// 获取分页参数
	limit := 50
	offset := 0
	if limitStr := c.Query("limit"); limitStr != "" {
		if limitVal, err := strconv.Atoi(limitStr); err == nil && limitVal > 0 {
			limit = limitVal
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offsetVal, err := strconv.Atoi(offsetStr); err == nil && offsetVal >= 0 {
			offset = offsetVal
		}
	}
	// 获取用户列表
	users, err := a.GetUsers(limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户列表失败: " + err.Error()})
		return
	}
	// 获取用户总数
	totalUsers, err := a.GetUserCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取用户总数失败: " + err.Error()})
		return
	}
	// 返回用户列表
	resp := AdminUserListResponse{
		Success: true,
		Users:   users,
		Count:   len(users),
		Total:   totalUsers,
	}
	c.JSON(http.StatusOK, resp)
}

// User 定义了用户信息的结构体，用于API响应
type User struct {
	CreatedAt    int64     `json:"created_at"`
	LastSignInAt int64     `json:"last_sign_in_at"`
	ID           uuid.UUID `json:"id"`
	Email        string    `json:"email"`
}

// GetUsers 从数据库获取用户列表，支持分页
func (a *AuthClient) GetUsers(limit, offset int) ([]User, error) {
	// 获取用户列表
	res, err := a.client.AdminListUsers()
	if err != nil {
		LogError("获取用户列表失败: %v", err)
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}
	users := res.Users
	// 转换用户数据
	var result []User
	for _, user := range users {
		result = append(result, User{
			ID:        user.ID,
			Email:     user.Email,
			CreatedAt: user.CreatedAt.UTC().Unix(),
			LastSignInAt: func() int64 {
				if user.LastSignInAt == nil {
					return 0
				}
				return user.LastSignInAt.UTC().Unix()
			}(),
		})
	}
	// 应用分页
	start := offset
	end := min(offset+limit, len(result))
	if start > len(result) {
		return []User{}, nil
	}
	return result[start:end], nil
}

// GetUserCount 获取用户总数
func (a *AuthClient) GetUserCount() (int, error) {
	// 获取用户列表
	res, err := a.client.AdminListUsers()
	if err != nil {
		LogError("获取用户列表失败: %v", err)
		return 0, fmt.Errorf("获取用户列表失败: %w", err)
	}
	return len(res.Users), nil
}

// AdminCreateUserRequest 定义了管理员创建用户的请求结构
type AdminCreateUserRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// AdminCreateUserResponse 定义了管理员创建用户的响应结构
type AdminCreateUserResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	UserID  string `json:"user_id,omitempty"`
}

// HandleAdminCreateUser 处理管理员创建用户的请求
// @Summary 创建用户账户
// @Description 管理员创建新的用户账户
// @Tags 管理员-用户管理
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} object "创建结果"
// @Failure 400 {object} SwaggerErrorResponse "请求参数错误"
// @Failure 401 {object} SwaggerErrorResponse "未授权"
// @Failure 403 {object} SwaggerErrorResponse "权限不足"
// @Router /api/v1/admin/user/create [post]
func (a *AuthClient) HandleAdminCreateUser(c *gin.Context) {
	// 只接受POST请求
	if c.Request.Method != http.MethodPost {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 从上下文中获取用户信息
	requestUser, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	// 检查管理员权限
	if !isAdmin(requestUser.ID.String()) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}
	// 解析请求体
	var req AdminCreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求格式错误"})
		return
	}
	// 验证请求
	if req.Email == "" || req.Password == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "邮箱和密码不能为空"})
		return
	}
	// 创建用户
	user, err := a.AdminCreateUser(req.Email, req.Password)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建用户失败: " + err.Error()})
		return
	}
	// 返回响应
	resp := AdminCreateUserResponse{
		Success: true,
		Message: "用户创建成功",
		UserID:  user.ID.String(),
	}
	c.JSON(http.StatusOK, resp)
}

// AdminCreateUser 管理员创建用户
func (a *AuthClient) AdminCreateUser(email, password string) (*User, error) {
	// 使用Supabase Admin API创建用户
	req := types.AdminCreateUserRequest{
		Email:        email,
		Password:     &password,
		EmailConfirm: true,
	}
	res, err := a.client.AdminCreateUser(req)
	if err != nil {
		LogError("创建用户失败: %v", err)
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}
	// 返回创建的用户信息
	return &User{
		ID:           res.ID,
		Email:        res.Email,
		CreatedAt:    res.CreatedAt.UTC().Unix(),
		LastSignInAt: 0,
	}, nil
}
