package main

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

var ErrInsufficientBalance = errors.New("insufficient balance")

// DBConfig 数据库配置 (字段按大小排序以优化内存对齐)
type DBConfig struct {
	ConnMaxLifetime time.Duration // 8字节
	ConnMaxIdleTime time.Duration // 8字节
	Host            string        // 16字节 (指针+长度)
	Port            string        // 16字节
	User            string        // 16字节
	Password        string        // 16字节
	Name            string        // 16字节
	MaxOpenConns    int           // 8字节
	MaxIdleConns    int           // 8字节
}

// DBManager 数据库连接管理器
type DBManager struct {
	db     *sqlx.DB
	config DBConfig
	mu     sync.RWMutex
	once   sync.Once
}

// PostgreSQLOperations PostgreSQL数据库操作实现（使用sqlx）
type PostgreSQLOperations struct {
	dbManager *DBManager
}

// SystemPrompt 系统提示词结构体
type SystemPrompt struct {
	ID          int       `json:"id" db:"id"`
	PromptKey   string    `json:"prompt_key" db:"prompt_key"`
	PromptText  string    `json:"prompt_text" db:"prompt_text"`
	Description string    `json:"description" db:"description"`
	Category    string    `json:"category" db:"category"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// WebsiteConfig 网站配置结构体（PostgreSQL）
type WebsiteConfig struct {
	ID      string `json:"id" db:"id"`           // 网站业务ID
	Name    string `json:"name" db:"name"`       // 网站名称
	Url     string `json:"url" db:"url"`         // 网站URL
	Actions string `json:"actions" db:"actions"` // 操作步骤数组 (JSON格式)
}

// Wallet 钱包结构体
type Wallet struct {
	PaidBalance int64     `json:"paid_balance" db:"paid_balance"` // 付费积分余额
	FreeBalance int64     `json:"free_balance" db:"free_balance"` // 赠送积分余额
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	UserID      string    `json:"user_id" db:"user_id"`
	ID          int       `json:"id" db:"id"`
}

// 定义充值状态枚举
type ApprovalStatus string

const (
	StatusPending  ApprovalStatus = "pending"
	StatusApproved ApprovalStatus = "approved"
	StatusRejected ApprovalStatus = "rejected"
)

// RechargeRequest 对应 recharge_requests 表
type RechargeRequest struct {
	ID            int            `db:"id"`
	UserID        string         `db:"user_id"`
	Amount        int            `db:"amount"`
	PointsToGrant int            `db:"points_to_grant"`
	OrderID       string         `db:"order_id"`
	Status        ApprovalStatus `db:"status"`
	PaymentMethod string         `db:"payment_method"`
	PaymentProof  sql.NullString `db:"payment_proof"`
	AdminNote     sql.NullString `db:"admin_note"`
	ProcessedBy   sql.NullString `db:"processed_by"`
	ProcessedAt   sql.NullTime   `db:"processed_at"`
	CreatedAt     time.Time      `db:"created_at"`
	UpdatedAt     time.Time      `db:"updated_at"`
}

// 实现 Valuer 接口，用于处理枚举值
func (s ApprovalStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// 实现 Scanner 接口，用于处理枚举值
func (s *ApprovalStatus) Scan(value any) error {
	if value == nil {
		*s = StatusPending
		return nil
	}
	str, ok := value.(string)
	if !ok {
		return errors.New("invalid approval status type")
	}
	switch str {
	case "pending", "approved", "rejected":
		*s = ApprovalStatus(str)
		return nil
	default:
		return fmt.Errorf("invalid approval status: %s", str)
	}
}

// TransactionSource 交易来源枚举
type TransactionSource string

const (
	TransactionSourceRecharge    TransactionSource = "RECHARGE"    // 付费充值
	TransactionSourceConsumption TransactionSource = "CONSUMPTION" // 业务消费
	TransactionSourceGiftSignup  TransactionSource = "GIFT_SIGNUP" // 注册赠送
	TransactionSourceGiftPromo   TransactionSource = "GIFT_PROMO"  // 活动赠送
	TransactionSourceRefund      TransactionSource = "REFUND"      // 退款
)

// Transaction 交易记录结构体
type Transaction struct {
	ID                   int64             `json:"id" db:"id"`                                         // 主键ID
	PaidPointsChange     int64             `json:"paid_points_change" db:"paid_points_change"`         // 付费积分变化量
	FreePointsChange     int64             `json:"free_points_change" db:"free_points_change"`         // 赠送积分变化量
	PaidBalanceAfter     int64             `json:"paid_balance_after" db:"paid_balance_after"`         // 交易后付费积分余额
	FreeBalanceAfter     int64             `json:"free_balance_after" db:"free_balance_after"`         // 交易后赠送积分余额
	CreatedAt            time.Time         `json:"created_at" db:"created_at"`                         // 创建时间戳
	UserID               string            `json:"user_id" db:"user_id"`                               // 用户ID，对应于Supabase中的auth.users.id
	Source               TransactionSource `json:"source" db:"source"`                                 // 交易来源
	Description          *string           `json:"description" db:"description"`                       // 交易描述（可选）
	RelatedConsumptionID *string           `json:"related_consumption_id" db:"related_consumption_id"` // 关联业务消费ID（可选）
	Metadata             *string           `json:"metadata" db:"metadata"`                             // 带有附加交易元数据的JSON字符串（可选）
	RelatedRechargeID    *int              `json:"related_recharge_id" db:"related_recharge_id"`       // 关联充值申请ID（可选）
}

// NewDBManager 创建数据库管理器
func NewDBManager() *DBManager {
	return &DBManager{
		config: DBConfig{
			Host:            GetDBHost(),
			Port:            GetDBPort(),
			User:            GetDBUser(),
			Password:        GetDBPassword(),
			Name:            GetDBName(),
			MaxOpenConns:    3,                // serverless环境减少连接数
			MaxIdleConns:    1,                // 减少空闲连接
			ConnMaxLifetime: 30 * time.Second, // 缩短连接生命周期
			ConnMaxIdleTime: 10 * time.Second, // 缩短空闲超时
		},
	}
}

// GetDB 获取数据库连接，支持连接重试和健康检查
func (dm *DBManager) GetDB() (*sqlx.DB, error) {
	dm.mu.RLock()
	if dm.db != nil {
		// 检查连接是否健康
		if err := dm.db.Ping(); err == nil {
			dm.mu.RUnlock()
			return dm.db, nil
		}
		// 连接不健康，需要重新创建
		dm.mu.RUnlock()
		dm.mu.Lock()
		if dm.db != nil {
			dm.db.Close()
			dm.db = nil
		}
		dm.mu.Unlock()
	} else {
		dm.mu.RUnlock()
	}

	// 使用sync.Once确保只初始化一次
	var err error
	dm.once.Do(func() {
		dm.mu.Lock()
		defer dm.mu.Unlock()
		dm.db, err = dm.initDB()
	})

	if err != nil {
		// 重置once，允许下次重试
		dm.once = sync.Once{}
		return nil, err
	}

	return dm.db, nil
}

// initDB 初始化数据库连接
func (dm *DBManager) initDB() (*sqlx.DB, error) {
	if dm.config.Host == "" {
		return nil, fmt.Errorf("DB_HOST environment variable not set")
	}

	// 构建DSN
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dm.config.Host, dm.config.Port, dm.config.User, dm.config.Password, dm.config.Name)

	// 创建连接
	conn, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 配置连接池参数（适配serverless环境）
	conn.SetMaxOpenConns(dm.config.MaxOpenConns)
	conn.SetMaxIdleConns(dm.config.MaxIdleConns)
	conn.SetConnMaxLifetime(dm.config.ConnMaxLifetime)
	conn.SetConnMaxIdleTime(dm.config.ConnMaxIdleTime)

	return conn, nil
}

// Close 关闭数据库连接
func (dm *DBManager) Close() error {
	dm.mu.Lock()
	defer dm.mu.Unlock()
	if dm.db != nil {
		err := dm.db.Close()
		dm.db = nil
		return err
	}
	return nil
}

// NewPostgreSQLOperations 创建PostgreSQL操作实例
func NewPostgreSQLOperations() *PostgreSQLOperations {
	return &PostgreSQLOperations{
		dbManager: NewDBManager(),
	}
}

// GetDBInstance 获取数据库实例（保持向后兼容）
func (p *PostgreSQLOperations) GetDBInstance() (*sqlx.DB, error) {
	return p.dbManager.GetDB()
}

// Close 关闭数据库连接
func (p *PostgreSQLOperations) Close() error {
	if p.dbManager != nil {
		return p.dbManager.Close()
	}
	return nil
}

// ==================== PostgreSQL操作方法 ====================

// GetActionsConfig 从PostgreSQL的website_configs表获取所有配置
func (p *PostgreSQLOperations) GetActionsConfig() ([]ConfigItem, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// 查询所有网站配置
	var configs []WebsiteConfig
	query := "SELECT id, name, url, actions FROM website_configs"
	err = dbconn.SelectContext(ctx, &configs, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get website configs: %v", err)
	}
	// 将WebsiteConfig转换为ConfigItem格式
	var configItems []ConfigItem
	for _, config := range configs {
		// 解析JSON格式的actions字段
		var actions []Action
		err = json.Unmarshal([]byte(config.Actions), &actions)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal actions for config %s: %v", config.ID, err)
		}
		// 创建ConfigItem
		configItem := ConfigItem{
			ID:      config.ID,
			Name:    config.Name,
			Url:     config.Url,
			Actions: actions,
		}
		configItems = append(configItems, configItem)
	}
	return configItems, nil
}

// GetSubjectPrompts 从PostgreSQL的system_prompts表获取科目提示词(只返回提示词key和description)
func (p *PostgreSQLOperations) GetSubjectPrompts() ([]Subject, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var prompts []SystemPrompt
	query := "SELECT id, prompt_key, prompt_text, description, category, created_at, updated_at FROM system_prompts"
	err = dbconn.SelectContext(ctx, &prompts, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get system prompts: %v", err)
	}
	// 将SystemPrompt转换为Subject格式
	var subjects []Subject
	for _, prompt := range prompts {
		subject := Subject{
			SubjectID:   prompt.PromptKey,
			SubjectName: prompt.Description,
		}
		subjects = append(subjects, subject)
	}
	return subjects, nil
}

func (p *PostgreSQLOperations) GetSystemPromptForSubject(subject string) (string, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return "", fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var prompt SystemPrompt
	query := "SELECT id, prompt_key, prompt_text, description, category, created_at, updated_at FROM system_prompts WHERE prompt_key = $1"
	err = dbconn.GetContext(ctx, &prompt, query, subject)
	if err != nil {
		return "", fmt.Errorf("failed to get system prompt: %v", err)
	}
	return prompt.PromptText, nil
}

// CreateRechargeRequest 创建新的充值申请
func (p *PostgreSQLOperations) CreateRechargeRequest(userID string, amount int, orderID string, paymentProof string) error {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	/*
	 * =========================================================================================
	 *  SQL 语句说明 (使用CTE):
	 *  1. `input_data` CTE:
	 *     - 将所有输入参数打包成一个虚拟的单行表。
	 *     - 在这里进行数据类型转换（如 $1::UUID）和业务逻辑计算（如根据 amount 计算 points_to_grant）。
	 *     - 假设充值规则为 1 元 = 1000 积分。
	 *  2. `INSERT ... SELECT ...` 主体:
	 *     - 从 `input_data` 中选择数据准备插入。
	 *     - `WHERE NOT EXISTS (...)` 子句是核心：它在插入前原子性地检查 `recharge_requests` 表中是否已存在相同的 `order_id`。
	 *     - 如果 `order_id` 已存在，`WHERE` 条件不满足，`SELECT` 不返回任何行，因此 `INSERT` 操作不会执行。
	 *     - 如果 `order_id` 不存在，则 `INSERT` 成功执行。
	 *  3. `RETURNING id`:
	 *     - 如果 `INSERT` 成功，它会返回新创建行的 `id`。
	 *     - 如果 `INSERT` 因为 `WHERE` 条件未满足而没有执行，它将不返回任何行。
	 * =========================================================================================
	 */
	query := `
		WITH input_data AS (
			SELECT
				$1::UUID AS user_id,
				-- 您的Go函数传入的是int，但数据库字段是NUMERIC(10, 2)，这里直接转换
				$2::NUMERIC AS amount,
				-- 根据表结构，自动计算需要授予的积分 (假设1元=1000积分)
				($2 * 1000)::INTEGER AS points_to_grant,
				$3 AS order_id,
				$4 AS payment_proof
		)
		INSERT INTO recharge_requests (user_id, amount, points_to_grant, order_id, payment_proof, status)
		SELECT
			id.user_id, id.amount, id.points_to_grant, id.order_id, id.payment_proof, 'pending'::approval_status
		FROM
			input_data id
		WHERE NOT EXISTS (
			SELECT 1 FROM recharge_requests rr WHERE rr.order_id = id.order_id
		)
		RETURNING id
	`
	var newRequestID int64 // 用于接收 RETURNING 返回的id
	// 使用 QueryRowContext, 因为我们期望返回0行(如果重复)或1行(如果成功)
	err = dbconn.QueryRowContext(ctx, query, userID, amount, orderID, paymentProof).Scan(&newRequestID)
	// 对返回的错误进行精细化处理
	if err != nil {
		// 如果错误是 sql.ErrNoRows, 这意味着 INSERT 没有执行任何操作。
		// 在我们的逻辑中，这精确地对应 "订单号已存在" 的情况。
		if errors.Is(err, sql.ErrNoRows) {
			return errors.New("订单号已存在，请勿重复提交")
		}
		// 如果是其他类型的错误, 则是真正的数据库执行错误。
		return fmt.Errorf("创建充值申请失败: %w", err)
	}
	return nil
}

// ProcessRechargeRequest 获取充值申请列表
func (p *PostgreSQLOperations) GetUserRechargeRequests(userID string, limit, offset int) ([]RechargeRequest, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var requests []RechargeRequest
	query := `SELECT
	  id,
		user_id,
		amount,
		points_to_grant,
		order_id,
		status,
		payment_method,
		payment_proof,
		admin_note,
		processed_by,
		processed_at,
		created_at,
		updated_at
	FROM
	  recharge_requests
	WHERE
	  user_id = $1
	ORDER BY
	  created_at DESC
	LIMIT $2
	OFFSET $3`
	err = dbconn.SelectContext(ctx, &requests, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("获取充值申请列表失败: %v", err)
	}
	return requests, nil
}

// GetRechargeRequests 获取充值申请列表
func (p *PostgreSQLOperations) GetRechargeRequests(status string, limit, offset int) ([]RechargeRequest, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var requests []RechargeRequest
	query := `
		SELECT
			id,
			user_id,
			amount,
			points_to_grant,
			order_id,
			status,
			payment_method,
			payment_proof,
			admin_note,
			processed_by,
			processed_at,
			created_at,
			updated_at
		FROM
			recharge_requests
		WHERE
			status = $1
		ORDER BY
			created_at DESC
		LIMIT $2
		OFFSET $3
	`
	err = dbconn.SelectContext(ctx, &requests, query, status, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("获取充值申请列表失败: %v", err)
	}
	return requests, nil
}

// ProcessRechargeRequest 处理充值申请（批准或拒绝）
func (p *PostgreSQLOperations) ProcessRechargeRequest(requestID int64, adminID string, action string, note string, rmbAmount int) error {
	if action != "approve" && action != "reject" {
		return errors.New("无效的操作，必须是 'approve' 或 'reject'")
	}
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	var req *RechargeRequest
	// 根据操作类型（批准/拒绝）执行不同的逻辑
	if action == "approve" {
		req, err = p.processApproval(ctx, dbconn, requestID, adminID, note, rmbAmount)
	} else { // action == "reject"
		req, err = p.processRejection(ctx, dbconn, requestID, adminID, note)
	}
	if err != nil {
		return err // 直接返回错误，具体的错误信息已在各函数中封装好
	}
	// 事务成功后，异步发送邮件通知
	go p.sendNotificationEmail(req, action)
	return nil
}

// processApproval 处理批准逻辑
func (p *PostgreSQLOperations) processApproval(ctx context.Context, db *sqlx.DB, requestID int64, adminID, note string, rmbAmount int) (*RechargeRequest, error) {
	/*
	 * =========================================================================================
	 * 整体说明：
	 * 这是一个用于“批准”充值请求的单一、原子性的SQL事务。
	 * 它通过一系列的CTE（WITH子句）来完成以下任务：
	 * 1.  `get_request`: 查找并锁定待处理的充值申请，验证其状态为'pending'。
	 * 2.  `approval_params`: 根据传入的`rmbAmount`确定最终充值金额和应授予的积分。如果`rmbAmount`无效(<=0)，则使用申请时的原始金额。
	 * 3.  `update_request_status`: 更新充值申请的状态为'approved'，记录处理信息。
	 * 4.  `ensure_wallet`: 确保用户钱包存在，如果不存在则创建。
	 * 5.  `update_wallet`: 更新用户钱包的付费余额。
	 * 6.  `insert_transaction`: 在交易表中记录此次充值。
	 *
	 * 优点：
	 * - 原子性：所有操作捆绑在单一事务中，要么全部成功，要么全部失败。
	 * - 数据一致性：通过 `FOR UPDATE` 锁定行，防止并发处理同一个请求。
	 * - 高效性：一次数据库交互完成所有逻辑，无需在应用层进行预查询。
	 *
	 * 参数说明：
	 * $1: requestID (BIGINT)     - 充值申请的ID
	 * $2: adminID (UUID)         - 处理该请求的管理员ID
	 * $3: note (TEXT)            - 管理员备注
	 * $4: rmbAmount (INTEGER)    - 最终确认的充值金额（人民币）。如果<=0，则使用数据库中的原始金额。
	 * =========================================================================================
	 */
	const approveQuery = `
    WITH get_request AS (
        -- 步骤1: 查找并锁定待处理的充值申请
        SELECT id, user_id, amount, order_id
        FROM recharge_requests
        WHERE id = $1 AND status = 'pending'
        FOR UPDATE
    ),
    approval_params AS (
        -- 步骤2: 计算最终金额和积分
        SELECT
            gr.id,
            gr.user_id,
            gr.order_id,
            gr.amount AS original_amount,
            -- 如果传入的rmbAmount ($4) > 0，则使用它，否则使用数据库中的原始金额
            CASE
                WHEN $4 > 0 THEN $4::NUMERIC
                ELSE gr.amount
            END AS final_amount,
            -- 根据最终金额计算积分 (1元 = 1000积分)
            (CASE
                WHEN $4 > 0 THEN $4::NUMERIC
                ELSE gr.amount
            END * 1000)::BIGINT AS points_to_grant
        FROM get_request gr
    ),
    update_request_status AS (
        -- 步骤3: 更新申请状态为 'approved'
        UPDATE recharge_requests
        SET
            status = 'approved',
            processed_by = $2::UUID,
            admin_note = $3,
            amount = ap.final_amount, -- 使用计算出的最终金额
            points_to_grant = ap.points_to_grant, -- 使用计算出的积分
            processed_at = NOW(),
            updated_at = NOW()
        FROM approval_params ap
        WHERE recharge_requests.id = ap.id
        RETURNING id, user_id, amount, points_to_grant
    ),
    ensure_wallet AS (
        -- 步骤4: 确保钱包存在，不存在则创建
        INSERT INTO wallets (user_id, paid_balance, free_balance)
        SELECT ur.user_id, 0, 0
        FROM update_request_status ur
        ON CONFLICT (user_id) DO NOTHING
        RETURNING id, user_id
    ),
    update_wallet AS (
        -- 步骤5: 增加用户付费积分
        UPDATE wallets w
        SET
            paid_balance = w.paid_balance + ur.points_to_grant,
            updated_at = NOW()
        FROM update_request_status ur
        WHERE w.user_id = ur.user_id
        RETURNING w.user_id, w.paid_balance, w.free_balance
    )
    -- 步骤6: 插入交易记录
    INSERT INTO transactions (
        user_id, source, paid_points_change, free_points_change,
        paid_balance_after, free_balance_after, description,
        related_recharge_id, metadata
    )
    SELECT
        uw.user_id,
        'RECHARGE'::transaction_source,
        ur.points_to_grant,
        0, -- 付费充值不影响赠送余额
        uw.paid_balance,
        uw.free_balance,
        'approved by admin',
        ur.id,
        jsonb_build_object('admin_id', $2, 'note', $3, 'original_amount', ap.original_amount)
    FROM
        update_wallet uw
    JOIN
        update_request_status ur ON uw.user_id = ur.user_id
    JOIN
        approval_params ap ON ur.id = ap.id
    RETURNING ur.id, ur.user_id, ap.order_id, ur.amount;
    `
	var approvedRequest RechargeRequest
	err := db.QueryRowxContext(ctx, approveQuery, requestID, adminID, note, rmbAmount).StructScan(&approvedRequest)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("处理失败：该充值申请(ID: %d)不存在、已被处理或状态不是'pending'", requestID)
		}
		return nil, fmt.Errorf("执行批准操作失败: %w", err)
	}
	return &approvedRequest, nil
}

// processRejection 处理拒绝逻辑
func (p *PostgreSQLOperations) processRejection(ctx context.Context, db *sqlx.DB, requestID int64, adminID, note string) (*RechargeRequest, error) {
	const rejectQuery = `
        UPDATE recharge_requests
        SET
            status = 'rejected',
            processed_by = $2::UUID,
            admin_note = $3,
            processed_at = NOW(),
            updated_at = NOW()
        WHERE
            id = $1 AND status = 'pending'
		RETURNING id, user_id, order_id, amount
    `
	var rejectedRequest RechargeRequest
	err := db.QueryRowxContext(ctx, rejectQuery, requestID, adminID, note).StructScan(&rejectedRequest)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("处理失败：该充值申请(ID: %d)不存在、已被处理或状态不是'pending'", requestID)
		}
		return nil, fmt.Errorf("执行拒绝操作失败: %w", err)
	}
	return &rejectedRequest, nil
}

// sendNotificationEmail 异步发送邮件通知
func (p *PostgreSQLOperations) sendNotificationEmail(req *RechargeRequest, action string) {
	if services == nil || services.AuthClient == nil {
		LogWarn("警告: authClient未初始化，无法发送邮件通知")
		return
	}
	if req == nil {
		LogError("sendNotificationEmail: 收到空的充值请求指针，无法发送邮件")
		return
	}
	var mailErr error
	switch action {
	case "approve":
		// 注意：req.Amount 是处理后的最终金额
		mailErr = services.AuthClient.SendRechargeSuccessEmailBySMTP(req.UserID, req.OrderID, req.Amount, req.Amount*1000)
	case "reject":
		mailErr = services.AuthClient.SendRechargeFailureEmailBySMTP(req.UserID, req.OrderID)
	}
	if mailErr != nil {
		LogError("发送充-值'%s'通知邮件失败 (RequestID: %d): %v", action, req.ID, mailErr)
	}
}

// EnsureWalletExists 确保用户钱包存在，如果是新用户则创建钱包并赠送500积分
func (p *PostgreSQLOperations) EnsureWalletExists(userID uuid.UUID) error {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 6*time.Second)
	defer cancel()
	// 这条单一的、原子的SQL语句处理了检查和创建的全部逻辑。它使用了CTE技术，确保了原子性和一致性。
	const ensureWalletSQL = `
	  -- 步骤1: 尝试插入新钱包。
    -- 这里的 WHERE NOT EXISTS 子句是关键，它确保只有当钱包不存在时，INSERT 才会执行。
		WITH new_wallet AS (
			INSERT INTO wallets (user_id, paid_balance, free_balance, created_at, updated_at)
			SELECT $1, 0, 500, NOW(), NOW()
			WHERE NOT EXISTS (
				SELECT 1 FROM wallets WHERE user_id = $1
			)
			-- 步骤2: 如果插入成功，返回新钱包的数据。
      -- 如果 WHERE 条件为假（即钱包已存在），INSERT 不会执行，RETURNING 也不会返回任何行。
			RETURNING user_id, paid_balance, free_balance
		)
		-- 步骤3: 插入交易记录。
    -- 这个 INSERT 只会在 'new_wallet' CTE 有数据时（即上一步成功创建了钱包时）执行。
		INSERT INTO transactions (
			user_id, source, paid_points_change, free_points_change,
			paid_balance_after, free_balance_after, description, created_at
		)
		SELECT
			nw.user_id,
			'GIFT_SIGNUP'::transaction_source,
			0,
			500,
			nw.paid_balance,
			nw.free_balance,
			'New user registration gift: 500 points',
			NOW()
		FROM new_wallet nw;
	`
	// 我们只需要执行这条语句。如果钱包已存在，它不会做任何事，也不会报错。
	// 并且我们只需要传递 userID 一个参数，时间戳由数据库的 NOW() 函数处理，保证一致性。
	_, err = dbconn.ExecContext(ctx, ensureWalletSQL, userID)
	if err != nil {
		return fmt.Errorf("failed to execute ensure-wallet operation: %v", err)
	}
	return nil
}

// GetUserBalance 获取用户总余额（付费积分 + 赠送积分）
func (p *PostgreSQLOperations) GetUserBalance(userID string) (int, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return 0, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var totalBalance int
	query := "SELECT (paid_balance + free_balance) AS total_balance FROM wallets WHERE user_id = $1"
	err = dbconn.GetContext(ctx, &totalBalance, query, userID)
	if err != nil {
		return 0, fmt.Errorf("failed to get user balance: %v", err)
	}
	return totalBalance, nil
}

// GetUserInfo 获取用户信息
func (p *PostgreSQLOperations) GetUserInfo(userID string) (map[string]any, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// 使用单一的、原子性的SQL查询，通过LEFT JOIN和条件聚合一次性获取钱包余额和已批准的充值总额。
	const query = `
		SELECT
			w.paid_balance,
			w.free_balance,
			COALESCE(SUM(rr.amount) FILTER (WHERE rr.status = 'approved'), 0) AS total_recharge
		FROM
			wallets w
		LEFT JOIN
			recharge_requests rr ON w.user_id = rr.user_id
		WHERE
			w.user_id = $1
		GROUP BY
			w.id
	`
	var result struct {
		PaidBalance   int64 `db:"paid_balance"`
		FreeBalance   int64 `db:"free_balance"`
		TotalRecharge int   `db:"total_recharge"`
	}
	err = dbconn.GetContext(ctx, &result, query, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// 如果用户钱包不存在，返回一个特定的错误或零值信息
			return nil, fmt.Errorf("user wallet not found for user_id: %s", userID)
		}
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	// 构建用户信息
	userInfo := map[string]any{
		"user_id":      userID,
		"balance":      result.PaidBalance + result.FreeBalance,
		"paid_balance": result.PaidBalance,
		"free_balance": result.FreeBalance,
		"recharge":     result.TotalRecharge,
	}
	return userInfo, nil
}

// ManualAdjustBalance 管理员手动给用户增加赠送积分
func (p *PostgreSQLOperations) ManualAdjustBalance(userID string, amount int, adminID string, reason string) (int, error) {
	if amount <= 0 {
		return 0, errors.New("amount must be positive for gift points")
	}
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return 0, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	now := time.Now()
	metadata := map[string]any{
		"aid": adminID,
	}
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal metadata: %v", err)
	}
	// 使用CTE原子性地更新钱包余额并记录交易
	adjustBalanceSQL := `
	  -- CTE 1: 检查钱包是否存在
    -- 这是一个简单的 SELECT，用于确认目标 user_id 在 wallets 表中是否存在。
    -- 如果存在，这个 CTE 会包含该用户的 user_id, paid_balance, 和 free_balance。
    -- 如果不存在，这个 CTE 将是空的。
		WITH wallet_check AS (
			SELECT user_id, paid_balance, free_balance
			FROM wallets
			WHERE user_id = $1
		),
		updated_wallet AS (
		  -- CTE 2: 更新钱包余额 (这是一个 Writable CTE)
      -- 这个 UPDATE 语句负责给用户的 free_balance 增加积分。
			UPDATE wallets
			SET free_balance = free_balance + $2, updated_at = $3
			WHERE user_id = $1 AND EXISTS(SELECT 1 FROM wallet_check)
			-- 关键的检查：AND EXISTS(SELECT 1 FROM wallet_check)
      -- 这个条件确保只有在第一个CTE 'wallet_check' 找到了用户的情况下，UPDATE才会执行。
      -- 如果 'wallet_check' 是空的 (即用户不存在)，这个 UPDATE 不会更新任何行。
			RETURNING user_id, paid_balance, free_balance
		)
		INSERT INTO transactions (
			user_id, source, paid_points_change, free_points_change,
			paid_balance_after, free_balance_after, description, metadata, created_at
		)
		SELECT
			-- 从 'updated_wallet' CTE 中获取数据来填充新交易记录
      uw.user_id,                         -- 用户ID
      'GIFT_PROMO'::transaction_source,   -- 交易来源类型
      0,                                  -- 付费积分变动为0
      $2,                                 -- 赠送积分变动量 (参数2: amount)
      uw.paid_balance,                    -- 调整后的付费余额 (未变)
      uw.free_balance,                    -- 调整后的赠送余额 (已增加)
      $4,                                 -- 描述 (参数4: reason)
      $5::jsonb,                          -- 元数据 (参数5: metadataJSON)
      $6                                  -- 创建时间 (参数6: now)
		FROM updated_wallet uw
		RETURNING (paid_balance_after + free_balance_after) AS total_balance;
	`
	var totalBalance int
	err = dbconn.QueryRowContext(ctx, adjustBalanceSQL, userID, amount, now, reason, string(metadataJSON), now).Scan(&totalBalance)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, errors.New("user wallet does not exist, please register and login first")
		}
		return 0, fmt.Errorf("failed to adjust balance and record transaction: %v", err)
	}
	return totalBalance, nil
}

// GetTransactionRecords 获取交易记录
func (p *PostgreSQLOperations) GetTransactionRecords(userID string, source string, startTime, endTime time.Time, limit, offset int) ([]Transaction, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	var transactions []Transaction
	// 基础查询语句
	query := `
        SELECT
            id,
            user_id,
            source,
            paid_points_change,
            free_points_change,
            paid_balance_after,
            free_balance_after,
            description,
            related_recharge_id,
            related_consumption_id,
            metadata,
            created_at
        FROM
            transactions
    `
	// 使用一个字符串切片来构建WHERE子句，更安全、更清晰
	conditions := []string{"1 = 1"}
	args := []any{}
	// 添加过滤条件
	if userID != "" {
		args = append(args, userID)
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", len(args)))
	}
	if source != "" {
		args = append(args, source)
		conditions = append(conditions, fmt.Sprintf("source = $%d", len(args)))
	}
	if !startTime.IsZero() {
		args = append(args, startTime)
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", len(args)))
	}
	if !endTime.IsZero() {
		args = append(args, endTime)
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", len(args)))
	}
	// 组合最终的查询语句
	fullQuery := query + " WHERE " + strings.Join(conditions, " AND ")
	fullQuery += fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d OFFSET $%d", len(args)+1, len(args)+2)
	args = append(args, limit, offset)
	// 执行查询
	err = dbconn.SelectContext(ctx, &transactions, fullQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction records: %w", err)
	}
	return transactions, nil
}

// GetUserTransactionRecords 获取用户交易记录
func (p *PostgreSQLOperations) GetUserTransactionRecords(userId string, limit int, offset int) ([]Transaction, error) {
	// 调用新的 GetTransactionRecords 方法，不指定 source 和时间范围
	return p.GetTransactionRecords(userId, "", time.Time{}, time.Time{}, limit, offset)
}

// DailyStatistics 表示每日充值统计
type DailyStatistics struct {
	Date          string `json:"date"`
	TotalAmount   int    `json:"total_amount"`   // 人民币金额
	RequestCount  int    `json:"request_count"`  // 充值请求数
	ApprovedCount int    `json:"approved_count"` // 充值成功数
	RejectedCount int    `json:"rejected_count"` // 充值失败数
	UpdatedAt     int64  `json:"updated_at"`     // 统计更新时间
}

// GetRechargeStatistics 获取充值统计
func (p *PostgreSQLOperations) GetRechargeStatistics(startDate, endDate string) ([]DailyStatistics, error) {
	// TODO
	return []DailyStatistics{}, nil
}

// 原子性地检查余额并消费（优先消费赠送积分，再消费付费积分）
func (p *PostgreSQLOperations) CheckAndConsume(userId string, amount int, description string, metadata map[string]any) (map[string]any, error) {
	if amount <= 0 {
		return nil, errors.New("consumption amount must be positive")
	}
	var metadataJSON string
	if metadata != nil {
		jsonBytes, err := json.Marshal(metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal metadata to JSON: %w", err)
		}
		metadataJSON = string(jsonBytes)
	} else {
		metadataJSON = "{}"
	}
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 6*time.Second)
	defer cancel()
	/*
	 * =========================================================================================
	 * 整体说明：
	 * 这是一个用于处理用户积分消费的单一、原子性的SQL语句。
	 * 它通过一系列的CTE（WITH子句）来完成以下任务：
	 * 1. 检查用户余额是否充足。
	 * 2. 计算应从“赠送积分”和“付费积分”中各自扣除多少（优先扣除赠送积分）。
	 * 3. 如果余额充足，则更新钱包余额。
	 * 4. 记录一笔详细的消费交易日志。
	 * 5. 返回消费后用户的最终总积分。
	 *
	 * 优点：
	 * - 原子性：整个操作要么全部成功，要么全部失败，避免了数据不一致的风险。
	 * - 高效性：通过一次数据库往返完成所有操作，减少了网络延迟。
	 * - 无竞态条件：在单个查询中完成检查和更新，避免了 "检查-然后-操作" 模式中可能出现的竞态条件。
	 *
	 * 参数说明：
	 * $1: UUID      - 进行消费操作的用户ID (user_id)
	 * $2: NUMERIC   - 本次需要消费的积分总额
	 * $3: TEXT      - 交易描述 (e.g., "AI阅卷服务")
	 * $4: JSONB     - 额外的元数据 (e.g., {"task_id": "xyz-123"})
	 * =========================================================================================
	 */
	atomicConsumptionSQL := `
	  -- 步骤 1: 获取用户当前的钱包状态
		WITH current_balance AS (
		  -- 这个CTE的作用是获取用户当前的钱包状态，并计算出总余额。
			SELECT
        paid_balance,
        free_balance,
        (paid_balance + free_balance) AS total_balance
			FROM wallets
			WHERE user_id = $1
		),
		-- 步骤 2: 制定消费计划 (纯计算，不修改数据)
		consumption_plan AS (
      -- 它根据 'current_balance' 的数据来决定需要从两种余额中各扣除多少。
			SELECT
			  -- 计算需要从'赠送积分'(free_balance)中消耗多少 (free_consumed)
				CASE
					WHEN total_balance < $2 THEN NULL  -- 关键：如果总余额不足，返回NULL。这个NULL将阻止后续的UPDATE和INSERT操作。
					WHEN free_balance >= $2 THEN $2    -- 如果赠送积分足够支付全部费用，则全部从赠送积分中扣除。
					ELSE free_balance                  -- 如果赠送积分不够，则先将所有赠送积分用完。
				END AS free_consumed,
				-- 计算需要从'付费积分'(paid_balance)中消耗多少 (paid_consumed)
				CASE
					WHEN total_balance < $2 THEN NULL  -- 余额不足时返回NULL。
					WHEN free_balance >= $2 THEN 0     -- 如果赠送积分已足够，则不需要消耗付费积分。
					ELSE $2 - free_balance             -- 否则，从付费积分中扣除剩余的部分 ($2是总消费额)。
				END AS paid_consumed
			FROM current_balance
		),
		-- 步骤 3: 更新钱包余额
		updated_wallet AS (
		  -- 它仅在 'consumption_plan' 计算出有效计划时（即余额充足）才会执行。
			UPDATE wallets
			SET
				paid_balance = paid_balance - cp.paid_consumed,
				free_balance = free_balance - cp.free_consumed,
				updated_at = NOW()
			FROM
			  consumption_plan cp
			WHERE
			  wallets.user_id = $1
				AND cp.free_consumed IS NOT NULL -- 安全阀：只有当 'consumption_plan' 成功（即余额充足，free_consumed不为NULL）时，才执行更新。
			RETURNING
			  user_id, paid_balance, free_balance
		)
		INSERT INTO
		  transactions (
			  user_id,
				source,
				paid_points_change,
				free_points_change,
			  paid_balance_after,
				free_balance_after,
				description,
				metadata,
				created_at
		  )
		SELECT
			uw.user_id,
			'CONSUMPTION'::transaction_source,
			-COALESCE(cp.paid_consumed, 0), -- 记录付费积分的变化量（负数表示减少）。COALESCE用于处理理论上不存在的NULL情况，更安全。
			-COALESCE(cp.free_consumed, 0), -- 记录赠送积分的变化量（负数）
			uw.paid_balance,
			uw.free_balance,
			$3,
			$4,
			NOW()
		FROM
		  updated_wallet uw,
			consumption_plan cp
		RETURNING
		  (paid_balance_after + free_balance_after) AS total_balance_after;
	`
	var newTotalBalance int
	err = dbconn.QueryRowxContext(ctx, atomicConsumptionSQL, userId, amount, description, metadataJSON).Scan(&newTotalBalance)
	if err != nil {
		// 检查错误是否为 sql.ErrNoRows
		// 如果是，说明CTE没有返回行，即余额不足。
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrInsufficientBalance
		}
		// 如果是其他错误，则是数据库或连接问题
		return nil, fmt.Errorf("database error during consumption: %w", err)
	}

	return map[string]any{
		"success": true,
		"balance": newTotalBalance,
	}, nil
}

// Expense 表示数据库中支出记录的结构
type Expense struct {
	ID                 int64  `json:"id" db:"id"`
	Description        string `json:"description" db:"description"`
	Amount             int64  `json:"amount" db:"amount"`
	Currency           string `json:"currency" db:"currency"`
	Category           string `json:"category" db:"category"`
	Vendor             string `json:"vendor" db:"vendor"`
	TransactionDate    string `json:"transaction_date" db:"transaction_date"`
	ServicePeriodStart string `json:"service_period_start" db:"service_period_start"`
	ServicePeriodEnd   string `json:"service_period_end" db:"service_period_end"`
	PaymentMethod      string `json:"payment_method" db:"payment_method"`
	InvoiceURL         string `json:"invoice_url" db:"invoice_url"`
	RecordedBy         string `json:"recorded_by" db:"recorded_by"`
	CreatedAt          string `json:"created_at" db:"created_at"`
}

// CreateExpense 添加一条新的支出记录
func (p *PostgreSQLOperations) CreateExpense(req AddExpenseRequest, recordedBy string) error {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %v", err)
	}
	query := `
        INSERT INTO expenses (description, amount, category, vendor, transaction_date, service_period_start, service_period_end, payment_method, invoice_url, recorded_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `
	_, err = dbconn.Exec(query, req.Description, req.Amount, req.Category, req.Vendor, req.TransactionDate, req.ServicePeriodStart, req.ServicePeriodEnd, req.PaymentMethod, req.InvoiceURL, recordedBy)
	if err != nil {
		return fmt.Errorf("failed to create expense: %w", err)
	}
	return nil
}

// GetComprehensiveFinancialReport 生成指定期间的财务报告
func (p *PostgreSQLOperations) GetComprehensiveFinancialReport(startDate, endDate string) (map[string]any, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	/*
	 * =========================================================================================
	 *  财务报告查询：计算指定时间段内的净利润
	 * =========================================================================================
	 *
	 *  整体目标:
	 *  本查询旨在生成一份精确的财务报告，计算在给定时间段内的总收入、总支出和净利润。
	 *
	 *  核心概念:
	 *  1. 总收入 (Revenue):     通过统计用户在业务中消费的“付费积分”来计算。
	 *  2. 运营支出 (Expenses):   采用“摊销”会计方法，将长期服务费用（如年付服务器）
	 *                           精确分摊到报告周期内的每一天，计算出应承担的成本。
	 *  3. 净利润 (Profit):       净利润 = 总收入 - 摊销后的运营支出。
	 *
	 *  技术实现:
	 *  使用一系列的公共表表达式 (CTE) 将复杂计算分解为清晰、可维护的逻辑步骤。
	 *
	 *  参数说明:
	 *  $1: TEXT -- 报告的开始日期 (例如: '2023-11-01')
	 *  $2: TEXT -- 报告的结束日期 (例如: '2023-12-01')，查询将包含此日期之前的所有数据。
	 *
	 */
	query := `
		WITH
		-- CTE 1: report_period - 定义报告周期
		report_period AS (
			SELECT
				$1::date AS start_date, -- 报告开始日期
				$2::date AS end_date    -- 报告结束日期
		),

		-- CTE 2: monthly_revenue - 计算总收入
		monthly_revenue AS (
			SELECT
			  -- paid_points_change是负数，取反后累加得到消耗的总积分。
				COALESCE(SUM(-paid_points_change), 0) / 1000.0 AS total_revenue
			FROM
				transactions, report_period
			WHERE
				source = 'CONSUMPTION'
				AND paid_points_change < 0 -- 只统计付费积分的减少部分
				AND created_at >= report_period.start_date
				AND created_at < report_period.end_date
		),

		-- CTE 3: operational_expenses - 计算摊销后的运营支出
		operational_expenses AS (
			SELECT
				COALESCE(
					SUM(
						-- 每日成本 * 在报告期内的有效天数
						(amount::numeric / (service_period_end - service_period_start + 1)) *
						GREATEST(0,
							(LEAST(report_period.end_date, service_period_end + 1) - GREATEST(report_period.start_date, service_period_start))::integer
						)
					),
				0) / 100.0 AS total_opex
			FROM
				expenses, report_period
			WHERE
				-- 筛选服务周期与报告周期有重叠的支出项目
				service_period_start < report_period.end_date
				AND service_period_end >= report_period.start_date
		)

		-- 最终查询: 汇总结果并计算利润
		SELECT
			r.total_revenue AS "monthly_revenue",
			o.total_opex AS "monthly_opex",
			(r.total_revenue - o.total_opex) AS "monthly_profit"
		FROM
			monthly_revenue r,
			operational_expenses o;
	`
	var report struct {
		MonthlyRevenue float64 `db:"monthly_revenue"`
		MonthlyOpex    float64 `db:"monthly_opex"`
		MonthlyProfit  float64 `db:"monthly_profit"`
	}
	err = dbconn.Get(&report, query, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get financial report: %w", err)
	}
	return map[string]any{
		"monthly_revenue": report.MonthlyRevenue,
		"monthly_opex":    report.MonthlyOpex,
		"monthly_profit":  report.MonthlyProfit,
	}, nil
}

// GetExpenseAnalysisByCategory 提供某段时间内按类别划分的支出明细
func (p *PostgreSQLOperations) GetExpenseAnalysisByCategory(startDate, endDate string) ([]map[string]any, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	query := `
        WITH report_period AS (
            SELECT $1::date AS start_date, $2::date AS end_date
        )
        SELECT
            category,
            -- 计算报告期内每个类别的摊销成本
            SUM(
						    -- 计算该项支出的“每日成本”
                amount::numeric / (service_period_end - service_period_start + 1) * 
						    -- 计算该项支出在报告期内的有效天数
                GREATEST(0, (LEAST(report_period.end_date, service_period_end + 1) - GREATEST(report_period.start_date, service_period_start))::integer)
            ) / 100.0 AS "monthly_cost"
        FROM expenses, report_period
        WHERE
            -- 筛选出服务周期与报告周期有重叠的支出项目
            service_period_start < report_period.end_date
            AND service_period_end >= report_period.start_date
        GROUP BY category
        ORDER BY "monthly_cost" DESC;
    `
	var analysis []struct {
		Category    string  `db:"category"`
		MonthlyCost float64 `db:"monthly_cost"`
	}
	err = dbconn.Select(&analysis, query, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get expense analysis: %w", err)
	}
	result := make([]map[string]any, len(analysis))
	for i, item := range analysis {
		result[i] = map[string]any{
			"category":     item.Category,
			"monthly_cost": item.MonthlyCost,
		}
	}
	return result, nil
}

// ListExpenses 从数据库中检索分页列出的支出记录
func (p *PostgreSQLOperations) ListExpenses(limit, offset int) ([]Expense, error) {
	dbconn, err := p.GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	query := `
        SELECT id, description, amount, currency, category, vendor, transaction_date, service_period_start, service_period_end, payment_method, invoice_url, recorded_by, created_at
        FROM expenses
        ORDER BY transaction_date DESC
        LIMIT $1 OFFSET $2
    `
	var expenses []Expense
	err = dbconn.Select(&expenses, query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list expenses: %w", err)
	}
	return expenses, nil
}
