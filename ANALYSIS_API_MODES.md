# Analysis API 四种模式说明

## 概述

`/api/v2/chat/analysis` 接口现在支持四种不同的分析模式，以满足不同的使用场景和成本需求。

## 四种分析模式

### 1. 传统模式 (Traditional Mode)

- **模式标识**: `"traditional"`
- **工作流程**:
  1. 使用 `doubao-1-5-vision-pro-32k-250115` 进行 OCR 识别
  2. 使用 `deepseek-v3-250324` 进行文本分析
- **特点**:
  - 最高精度的 OCR 识别
  - 最强的分析能力
  - 两步处理，成本最高
  - 返回 OCR 结果和分析结果
- **适用场景**: 重要考试、精确评分、复杂题目分析

### 2. 深度思考模式 (Thinking Mode)

- **模式标识**: `"thinking"`
- **工作流程**:
  - 直接使用 `doubao-seed-1-6-thinking-250715` 进行多模态分析
- **特点**:
  - 最强的多模态分析能力
  - 成本和耗时较高
  - 一步完成图像理解和分析
- **适用场景**: 复杂问题的深度分析、追求最高质量的单次分析

### 3. 智能均衡模式 (Standard Mode)

- **模式标识**: `"standard"`
- **工作流程**:
  - 直接使用 `doubao-seed-1-6-250615` 进行多模态分析
- **特点**:
  - 平衡的性能和成本
  - 一步完成图像理解和分析
  - 中等成本
  - 返回 OCR 结果和分析结果
- **适用场景**: 日常作业批改、一般性评估

### 4. 经济极速模式 (Economy Mode)

- **模式标识**: `"economy"`
- **工作流程**:
  - 直接使用 `doubao-seed-1-6-flash-250615` 进行快速多模态分析
- **特点**:
  - 最快的处理速度
  - 最低的成本
  - 适合快速预览和初步评估
  - 返回 OCR 结果和分析结果
- **适用场景**: 快速预览、批量处理、成本敏感场景

## API 请求格式

### 请求示例

```json
{
  "content": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/...",
  "text": "评分标准：1. 答案正确性 2. 解题步骤 3. 书写规范",
  "prompt_key": "math_analysis",
  "analysis_mode": "thinking",
  "temperature": 0.3
}
```

### 请求参数说明

| 参数            | 类型   | 必填 | 说明                          |
| --------------- | ------ | ---- | ----------------------------- |
| `content`       | string | 是   | 图片的 base64 编码            |
| `text`          | string | 是   | 评分标准或分析要求            |
| `prompt_key`    | string | 是   | 系统提示词的键值              |
| `analysis_mode` | string | 否   | 分析模式，默认为 `"standard"` |
| `temperature`   | float  | 否   | 温度参数，默认为 0.3          |

### 分析模式取值

- `"traditional"` - 传统模式
- `"thinking"` - 深度思考模式
- `"standard"` - 智能均衡模式（默认）
- `"economy"` - 经济极速模式

## API 响应格式

所有模式都遵循统一的响应格式和结构化输出。

### 统一结构化输出格式

```json
{
  "student_answer": "学生答案内容",
  "score": 85,
  "grading_details": "详细的评分说明"
}
```

**说明**：

- **传统模式**: `student_answer` 字段包含 OCR 识别的学生答案内容。
- **其他模式**: `student_answer` 字段包含从图片中直接提取的学生答案内容。
- 所有模式都包含相同的 `score` 和 `grading_details` 字段。

## 成本与性能对比

| 模式         | 模型/工作流                                | 相对成本 | 处理时间 | 核心优势       |
| ------------ | ------------------------------------------ | -------- | -------- | -------------- |
| 传统模式     | OCR + 分析 (2个模型)                       | 最高     | 最慢     | OCR精度最高    |
| 深度思考模式 | `doubao-seed-1-6-thinking-250715`          | 高       | 较慢     | 分析质量最强   |
| 智能均衡模式 | `doubao-seed-1-6-250615`                   | 中等     | 中等     | 性能成本均衡   |
| 经济极速模式 | `doubao-seed-1-6-flash-250615`             | 最低     | 最快     | 速度最快,便宜  |

## 使用建议

1. **传统模式**: 用于对OCR识别准确度有极高要求的场景。
2. **深度思考模式**: 用于复杂问题的深度分析，追求最高质量。
3. **智能均衡模式**: 用于日常作业批改、一般性评估，是性价比最高的选择。
4. **经济极速模式**: 用于快速预览、批量处理或成本极度敏感的场景。

## 错误处理

如果提供了无效的 `analysis_mode`，API 将返回 400 错误：

```json
{
  "error": "Invalid analysis_mode. Must be one of: traditional, thinking, standard, economy"
}
```

## 兼容性说明

- 如果不提供 `analysis_mode` 参数，默认使用 `"standard"` 模式。
- 旧版本的客户端可以继续正常工作，会自动使用智能均衡模式。
- 新增的模式不会影响现有客户端的解析。
