package main

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// AddExpenseRequest 定义了新增一笔支出记录的请求体结构。
// 用于管理员手动录入各项运营成本。
type AddExpenseRequest struct {
	Description        string `json:"description"`              // 支出描述，例如 "阿里云ECS服务器 2核4G 一年"
	Amount             int64  `json:"amount"`                   // 支付金额，单位：分
	Category           string `json:"category"`                 // 支出类别，必须是预定义的 expense_category 枚举值之一
	Vendor             string `json:"vendor"`                   // 供应商/收款方，例如 "阿里云", "GoDaddy"
	TransactionDate    string `json:"transaction_date"`         // 实际支付日期，格式：YYYY-MM-DD
	ServicePeriodStart string `json:"service_period_start"`     // 本次支付覆盖的服务周期的开始日期，格式：YYYY-MM-DD
	ServicePeriodEnd   string `json:"service_period_end"`       // 本次支付覆盖的服务周期的结束日期，格式：YYYY-MM-DD
	PaymentMethod      string `json:"payment_method,omitempty"` // 支付方式，例如 "alipay", "银行转账"
	InvoiceURL         string `json:"invoice_url,omitempty"`    // 发票或凭证的URL链接
}

// FinancialReportRequest 定义了请求财务报表的请求体结构。
// 通过指定开始和结束日期来定义报表周期。
type FinancialReportRequest struct {
	StartDate string `json:"start_date"` // 报表开始日期, 格式: YYYY-MM-DD
	EndDate   string `json:"end_date"`   // 报表结束日期, 格式: YYYY-MM-DD
}

// HandleAdminAddExpense 处理管理员新增支出记录的请求。
// @Summary 新增支出记录
// @Description 管理员权限，用于录入一笔新的运营支出。
// @Tags 管理员-财务管理
// @Accept json
// @Produce json
// @Param body body AddExpenseRequest true "支出详情"
// @Success 200 {object} object "支出记录创建成功"
// @Failure 400 {string} string "无效的请求格式或缺少必要字段"
// @Failure 401 {string} string "未授权"
// @Failure 403 {string} string "权限不足"
// @Failure 500 {string} string "创建支出记录失败"
// @Router /api/v1/admin/finance/expense [post]
func HandleAdminAddExpense(c *gin.Context) {
	if c.Request.Method != http.MethodPost {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 验证管理员身份
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	if !isAdmin(user.ID.String()) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}
	// 解析请求体
	var req AddExpenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求格式"})
		return
	}
	// 基本参数校验
	if req.Description == "" || req.Amount <= 0 || req.Category == "" || req.TransactionDate == "" || req.ServicePeriodStart == "" || req.ServicePeriodEnd == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "缺少必要字段"})
		return
	}
	// 调用数据库服务创建记录
	err := services.DB.CreateExpense(req, user.ID.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "创建支出记录失败: " + err.Error()})
		return
	}
	// 返回成功响应
	c.JSON(http.StatusOK, map[string]any{
		"success": true,
		"message": "支出记录创建成功",
	})
}

// HandleAdminGetFinancialReport 处理管理员获取综合财务报表的请求。
// @Summary 获取综合财务报表
// @Description 管理员权限，生成包含营收、成本和利润的财务报表。
// @Tags 管理员-财务管理
// @Accept json
// @Produce json
// @Param body body FinancialReportRequest true "报表周期"
// @Success 200 {object} object "财务报告"
// @Failure 400 {string} string "无效的请求格式或缺少必要字段"
// @Failure 401 {string} string "未授权"
// @Failure 403 {string} string "权限不足"
// @Failure 500 {string} string "获取财务报表失败"
// @Router /api/v1/admin/finance/report [post]
func HandleAdminGetFinancialReport(c *gin.Context) {
	if c.Request.Method != http.MethodPost {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 验证管理员身份
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	if !isAdmin(user.ID.String()) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}
	// 解析请求体
	var req FinancialReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求格式"})
		return
	}
	// 校验日期参数
	if req.StartDate == "" || req.EndDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "必须提供开始和结束日期"})
		return
	}
	// 调用数据库服务生成报表
	report, err := services.DB.GetComprehensiveFinancialReport(req.StartDate, req.EndDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取财务报表失败: " + err.Error()})
		return
	}
	// 返回报表数据
	c.JSON(http.StatusOK, map[string]any{
		"success": true,
		"report":  report,
	})
}

// HandleAdminGetExpenseAnalysis 处理管理员按分类获取支出分析的请求。
// @Summary 按分类获取支出分析
// @Description 管理员权限，按类别分析指定周期内的各项运营成本。
// @Tags 管理员-财务管理
// @Accept json
// @Produce json
// @Param body body FinancialReportRequest true "分析周期"
// @Success 200 {object} object "支出分析"
// @Failure 400 {string} string "无效的请求格式或缺少必要字段"
// @Failure 401 {string} string "未授权"
// @Failure 403 {string} string "权限不足"
// @Failure 500 {string} string "获取支出分析失败"
// @Router /api/v1/admin/finance/analysis [post]
func HandleAdminGetExpenseAnalysis(c *gin.Context) {
	if c.Request.Method != http.MethodPost {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 验证管理员身份
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	if !isAdmin(user.ID.String()) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}
	// 解析请求体
	var req FinancialReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求格式"})
		return
	}
	// 校验日期参数
	if req.StartDate == "" || req.EndDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "必须提供开始和结束日期"})
		return
	}
	// 调用数据库服务进行分析
	analysis, err := services.DB.GetExpenseAnalysisByCategory(req.StartDate, req.EndDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取支出分析失败: " + err.Error()})
		return
	}
	// 返回分析结果
	c.JSON(http.StatusOK, map[string]any{
		"success":  true,
		"analysis": analysis,
	})
}

// HandleAdminListExpenses 处理管理员列出所有支出记录的请求。
// @Summary 列出支出记录
// @Description 管理员权限，分页列出所有已录入的支出记录。
// @Tags 管理员-财务管理
// @Produce json
// @Param limit query int false "每页数量"
// @Param offset query int false "偏移量"
// @Success 200 {object} object "支出列表"
// @Failure 401 {string} string "未授权"
// @Failure 403 {string} string "权限不足"
// @Failure 500 {string} string "获取支出列表失败"
// @Router /api/v1/admin/finance/expenses [get]
func HandleAdminListExpenses(c *gin.Context) {
	if c.Request.Method != http.MethodGet {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "方法不允许"})
		return
	}
	// 验证管理员身份
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return
	}
	if !isAdmin(user.ID.String()) {
		c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		return
	}
	// TODO: 实现分页参数的获取
	limit, offset := 10, 0 // 默认值
	// 调用数据库服务获取列表
	expenses, err := services.DB.ListExpenses(limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取支出列表失败: " + err.Error()})
		return
	}
	// 返回支出列表
	c.JSON(http.StatusOK, map[string]any{
		"success":  true,
		"expenses": expenses,
	})
}
