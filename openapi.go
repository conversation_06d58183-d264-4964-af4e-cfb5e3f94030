package main

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"

	jsonrepair "github.com/RealAlexandreAI/json-repair"
	"github.com/gin-gonic/gin"
	"github.com/supabase-community/auth-go/types"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
)

// 火山引擎的模型列表
var ARK_MODELS_MAP = map[string]int{
	"deepseek-v3-250324":               1,
	"deepseek-r1-250120":               2,
	"doubao-1-5-vision-pro-32k-250115": 3,
	"doubao-1.5-vision-pro-250328":     4,
	"doubao-seed-1-6-250615":           5,
	"doubao-seed-1-6-flash-250615":     6,
	"doubao-seed-1-6-thinking-250715":  7,
}

// 默认成本倍率
const DEFAULT_COST_MULTIPLIER = 2

// 最大令牌数32k
const MaxTokens = 32000

// 全局模型价格映射
var MODEL_PRICING_MAP = map[string]ModelPricing{
	// 火山引擎模型价格
	"doubao-seed-1-6-250615":           {PromptPrice: 0.0008 / 1000, CompletionPrice: 0.002 / 1000},
	"doubao-seed-1-6-flash-250615":     {PromptPrice: 0.00015 / 1000, CompletionPrice: 0.0015 / 1000},
	"doubao-seed-1-6-thinking-250715":  {PromptPrice: 0.0008 / 1000, CompletionPrice: 0.008 / 1000},
	"doubao-1.5-vision-pro-250328":     {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"doubao-1-5-vision-pro-32k-250115": {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"deepseek-v3-250324":               {PromptPrice: 0.002 / 1000, CompletionPrice: 0.008 / 1000},
	"deepseek-r1-250120":               {PromptPrice: 0.004 / 1000, CompletionPrice: 0.016 / 1000},
}

// 默认价格
var DEFAULT_PRICING = ModelPricing{PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000}

// AnalysisMode 分析模式枚举
type AnalysisMode string

const (
	// TraditionalMode 传统模式：OCR + 分析
	TraditionalMode AnalysisMode = "traditional"
	// ThinkingMode 深度思考模式：直接多模态分析
	ThinkingMode AnalysisMode = "thinking"
	// StandardMode 智能均衡模式：直接多模态分析
	StandardMode AnalysisMode = "standard"
	// EconomyMode 经济极速模式：快速多模态分析
	EconomyMode AnalysisMode = "economy"
)

// ANALYSIS_MODE_MODELS 集中化模型配置
var ANALYSIS_MODE_MODELS = map[AnalysisMode]struct {
	PrimaryModel string
	OcrModel     string // 仅用于传统模式
}{
	TraditionalMode: {PrimaryModel: "deepseek-v3-250324", OcrModel: "doubao-1-5-vision-pro-32k-250115"},
	ThinkingMode:    {PrimaryModel: "doubao-seed-1-6-thinking-250715"},
	StandardMode:    {PrimaryModel: "doubao-seed-1-6-250615"},
	EconomyMode:     {PrimaryModel: "doubao-seed-1-6-flash-250615"},
}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	Text         string       `json:"text"`                    // 评分标准
	PromptKey    string       `json:"prompt_key"`              // 系统提示词的键值
	Content      string       `json:"content"`                 // 学生答案图片url
	AnalysisMode AnalysisMode `json:"analysis_mode,omitempty"` // 分析模式，仅用于analysis接口
}

// AnalysisResult 统一的分析结果结构
type AnalysisResult struct {
	StudentAnswer  string `json:"student_answer"`  // 学生答案内容（传统模式为OCR结果，其他模式为从图片提取的内容）
	Score          int    `json:"score"`           // 计算得到的最终数字得分
	GradingDetails string `json:"grading_details"` // 详细说明得分点和扣分点
}

// SimplifiedResponse 表示简化的响应
type SimplifiedResponse struct {
	ID       string          `json:"id"`
	Analysis *AnalysisResult `json:"analysis,omitempty"` // 结构化分析结果
	Balance  int             `json:"balance"`
}

// ModelPricing 表示模型价格
type ModelPricing struct {
	PromptPrice     float64
	CompletionPrice float64
}

// GetModelPricing 获取特定模型的价格
func GetModelPricing(model string) ModelPricing {
	// 根据模型名称选择价格
	pricing, exists := MODEL_PRICING_MAP[model]
	if !exists {
		return DEFAULT_PRICING
	}
	return pricing
}

// 计算API调用成本（积分制）
func calculateAPICostInPoints(usage model.Usage, pricing ModelPricing) int {
	// 计算提示和完成的成本（元）
	promptCost := float64(usage.PromptTokens) * pricing.PromptPrice
	completionCost := float64(usage.CompletionTokens) * pricing.CompletionPrice
	// 应用成本倍率
	totalCostYuan := (promptCost + completionCost) * DEFAULT_COST_MULTIPLIER
	// 转换为积分（1元=1000积分）并向上取整
	totalCostPoints := max(int(math.Ceil(totalCostYuan*1000)), 1)
	return totalCostPoints
}

// containsKey 检查字符串是否在map中
func containsKey(models map[string]int, items string) bool {
	if _, ok := models[items]; ok {
		return true
	}
	return false
}

func modelId(key string) int {
	if v, ok := ARK_MODELS_MAP[key]; ok {
		return v
	}
	return 0
}

// AnalysisRequest 表示分析请求的内部结构
type AnalysisRequest struct {
	User        *types.UserResponse
	RequestBody ChatRequest
	Context     context.Context
}

// InternalAnalysisResult 表示分析结果的内部结构
type InternalAnalysisResult struct {
	OcrResult        string                        // OCR结果（仅传统模式）
	AnalysisResponse *model.ChatCompletionResponse // 主要分析响应
	OcrResponse      *model.ChatCompletionResponse // OCR响应（仅传统模式）
	Mode             AnalysisMode                  // 使用的分析模式
	Models           []string                      // 使用的模型列表
	TotalCost        int                           // 总成本
}

// validateAnalysisRequest 验证分析请求
func validateAnalysisRequest(c *gin.Context) (*AnalysisRequest, bool) {
	// 只接受POST请求
	if c.Request.Method != http.MethodPost {
		c.JSON(http.StatusMethodNotAllowed, gin.H{"error": "Method Not Allowed"})
		return nil, false
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(c.Request.Context())
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未授权"})
		return nil, false
	}
	// 检查用户钱包余额
	firstBalance, err := services.DB.GetUserBalance(user.ID.String())
	if err != nil {
		LogError("获取余额失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get balance"})
		return nil, false
	}
	// 检查余额是否足够
	if firstBalance <= 100 { // 0.1元 = 100积分
		c.JSON(http.StatusPaymentRequired, gin.H{"error": "Insufficient balance"})
		return nil, false
	}
	// 解析请求体
	var requestBody ChatRequest
	if err = c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return nil, false
	}
	// 验证请求
	if requestBody.Content == "" || requestBody.PromptKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: content, prompt_key are required"})
		return nil, false
	}
	// 设置默认分析模式
	if requestBody.AnalysisMode == "" {
		requestBody.AnalysisMode = StandardMode // 默认使用智能均衡模式
	}
	// 验证分析模式
	switch requestBody.AnalysisMode {
	case TraditionalMode, ThinkingMode, StandardMode, EconomyMode:
		// 有效模式
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid analysis_mode. Must be one of: traditional, thinking, standard, economy"})
		return nil, false
	}
	return &AnalysisRequest{
		User:        user,
		RequestBody: requestBody,
		Context:     c.Request.Context(),
	}, true
}

// performAnalysisByMode 根据模式执行分析
func performAnalysisByMode(c *gin.Context, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	switch req.RequestBody.AnalysisMode {
	case TraditionalMode:
		return performTraditionalAnalysis(c, req)
	case ThinkingMode, StandardMode, EconomyMode:
		return performMultiModalAnalysis(c, req)
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid analysis mode"})
		return nil, false
	}
}

// performTraditionalAnalysis 传统模式：OCR + 分析
func performTraditionalAnalysis(c *gin.Context, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	// 从配置中获取模型
	modelConfig := ANALYSIS_MODE_MODELS[TraditionalMode]
	ocrModel := modelConfig.OcrModel
	analysisModel := modelConfig.PrimaryModel
	// 第一步：OCR处理
	ocrClient, err := clientManager.getArkClient()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create OCR API client"})
		return nil, false
	}
	// OCR提示词
	ocrUserPrompt := `Role:教育专用手写OCR引擎
Action:准确识别图片中学生手写答题内容
Constraint:仅识别中文和英文,忽略划掉的文本,数学公式用LaTeX格式,如 (E=mc^2)
Input:用户上传的学生答题图片
Ouput: 只输出识别的内容，不要返回其他内容`
	ocrResponse, err := ocrClient.CreateChatCompletionWithImage(
		req.Context,
		ocrModel,
		"",
		ocrUserPrompt,
		req.RequestBody.Content,
		0.3,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("OCR API error: %v", err)})
		return nil, false
	}
	ocrResult := *ocrResponse.Choices[0].Message.Content.StringValue
	// 第二步：分析处理
	analysisClient, err := clientManager.getArkClient()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create analysis API client"})
		return nil, false
	}
	// 获取系统提示词
	systemPrompt, _ := services.DB.GetSystemPromptForSubject(req.RequestBody.PromptKey)
	systemPrompt += `
	## 请按照以下JSON格式输出评分结果：
	{"score":<计算得到的最终数字得分>,"grading_details":"<根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容>"}
	`
	// 构建分析提示词
	analysisPrompt := fmt.Sprintf("评分标准:\n%s\n\n学生答案:\n%s", req.RequestBody.Text, ocrResult)
	var temperature float32 = 0.3
	analysisResponse, err := analysisClient.CreateChatCompletionWithText(
		req.Context,
		analysisModel,
		systemPrompt,
		analysisPrompt,
		temperature,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Analysis API error: %v", err)})
		return nil, false
	}
	return &InternalAnalysisResult{
		OcrResult:        ocrResult,
		AnalysisResponse: analysisResponse,
		OcrResponse:      ocrResponse,
		Mode:             TraditionalMode,
		Models:           []string{ocrModel, analysisModel},
	}, true
}

// performMultiModalAnalysis 执行通用的多模态分析（用于深度、标准、经济模式）
func performMultiModalAnalysis(c *gin.Context, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	// 从配置中获取当前模式的模型
	modelConfig, ok := ANALYSIS_MODE_MODELS[req.RequestBody.AnalysisMode]
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid analysis mode configuration"})
		return nil, false
	}
	arkModel := modelConfig.PrimaryModel
	client, err := clientManager.getArkClient()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create API client"})
		return nil, false
	}
	// 获取系统提示词
	systemPrompt, _ := services.DB.GetSystemPromptForSubject(req.RequestBody.PromptKey)
	// 构建分析提示词
	analysisPrompt := req.RequestBody.Text
	var temperature float32 = 0.3
	// 创建响应格式
	responseFormat := createStandardAnalysisResponseFormat()
	var thinkingType model.ThinkingType
	if req.RequestBody.AnalysisMode == StandardMode {
		// 仅在标准模式下禁用深度思考
		thinkingType = model.ThinkingTypeDisabled
	}
	response, err := client.CreateChatCompletionWithImageAndResponseFormat(
		req.Context,
		arkModel,
		systemPrompt,
		analysisPrompt,
		req.RequestBody.Content,
		temperature,
		responseFormat,
		thinkingType,
		MaxTokens,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("API error: %v", err)})
		return nil, false
	}
	return &InternalAnalysisResult{
		AnalysisResponse: response,
		Mode:             req.RequestBody.AnalysisMode,
		Models:           []string{arkModel},
	}, true
}

// calculateCostAndConsume 计算成本并记录消费
func calculateCostAndConsume(req *AnalysisRequest, result *InternalAnalysisResult) (int, error) {
	var totalCost int
	var metadata map[string]any

	switch result.Mode {
	case TraditionalMode:
		// 传统模式：OCR + 分析两个模型的成本
		ocrModel := result.Models[0]
		analysisModel := result.Models[1]

		ocrPricing := GetModelPricing(ocrModel)
		ocrCost := calculateAPICostInPoints(result.OcrResponse.Usage, ocrPricing)

		analysisPricing := GetModelPricing(analysisModel)
		analysisCost := calculateAPICostInPoints(result.AnalysisResponse.Usage, analysisPricing)

		totalCost = ocrCost + analysisCost

		metadata = map[string]any{
			"m":   string(result.Mode),
			"om":  modelId(ocrModel),                              // ocr_model
			"am":  modelId(analysisModel),                         // analysis_model
			"opt": result.OcrResponse.Usage.PromptTokens,          // ocr_prompt_tokens
			"oct": result.OcrResponse.Usage.CompletionTokens,      // ocr_completion_tokens
			"apt": result.AnalysisResponse.Usage.PromptTokens,     // analysis_prompt_tokens
			"act": result.AnalysisResponse.Usage.CompletionTokens, // analysis_completion_tokens
			"pk":  req.RequestBody.PromptKey,                      // prompt_key
			"dcm": DEFAULT_COST_MULTIPLIER,                        // cost_multiplier
		}

	case ThinkingMode, StandardMode, EconomyMode:
		// 深度思考/标准/经济模式：单个模型的成本
		model := result.Models[0]
		pricing := GetModelPricing(model)
		totalCost = calculateAPICostInPoints(result.AnalysisResponse.Usage, pricing)

		metadata = map[string]any{
			"m":   string(result.Mode),
			"pt":  result.AnalysisResponse.Usage.PromptTokens,     // prompt_tokens
			"ct":  result.AnalysisResponse.Usage.CompletionTokens, // completion_tokens
			"pk":  req.RequestBody.PromptKey,                      // prompt_key
			"dcm": DEFAULT_COST_MULTIPLIER,                        // cost_multiplier
		}
	}
	result.TotalCost = totalCost
	// 记录消费
	consumeResult, err := services.DB.CheckAndConsume(
		req.User.ID.String(),
		totalCost,
		"",
		metadata,
	)
	if err != nil {
		// 如果记录消费失败，记录错误但仍然返回响应
		LogError("记录消费失败: %v", err)
	}
	// 获取更新后的余额
	var balance int
	if consumeResult != nil && consumeResult["success"].(bool) {
		balance = consumeResult["balance"].(int)
	} else {
		// 如果消费失败或未记录消费，尝试获取当前余额
		balance, _ = services.DB.GetUserBalance(req.User.ID.String())
	}
	return balance, nil
}

// JSONSchemaMarshaler 实现 json.Marshaler 接口的 JSON Schema 包装器
type JSONSchemaMarshaler struct {
	Schema map[string]any
}

// MarshalJSON 实现 json.Marshaler 接口
func (j JSONSchemaMarshaler) MarshalJSON() ([]byte, error) {
	return json.Marshal(j.Schema)
}

// createStandardAnalysisResponseFormat 创建标准/经济模式的响应格式
func createStandardAnalysisResponseFormat() *model.ResponseFormat {
	// 直接定义 JSON Schema
	schema := map[string]any{
		"type": "object",
		"properties": map[string]any{
			"student_answer": map[string]any{
				"type":        "string",
				"description": "从图片中提取学生答案内容",
			},
			"score": map[string]any{
				"type":        "integer",
				"description": "计算得到的最终数字得分",
			},
			"grading_details": map[string]any{
				"type":        "string",
				"description": "根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容",
			},
		},
		"required": []string{"student_answer", "score", "grading_details"},
	}

	return &model.ResponseFormat{
		Type: model.ResponseFormatJSONSchema,
		JSONSchema: &model.ResponseFormatJSONSchemaJSONSchemaParam{
			Name:   "analysis_result",
			Schema: JSONSchemaMarshaler{Schema: schema},
			Strict: true,
		},
	}
}

// parseStructuredAnalysisResult 解析结构化分析结果
func parseStructuredAnalysisResult(content string, mode AnalysisMode, ocrResult string) (*AnalysisResult, error) {
	switch mode {
	case TraditionalMode:
		// 传统模式：deepseek-v3-250324 不支持模型级结构化输出，需要手动解析JSON
		// 可能包含markdown代码块，需要提取JSON部分
		jsonContent, err := jsonrepair.RepairJSON(content)
		if err != nil {
			LogError("JSON修复失败: %v, 内容: %s", err, content)
			return nil, err
		}
		var tempResult struct {
			Score          int    `json:"score"`
			GradingDetails string `json:"grading_details"`
		}
		if err := json.Unmarshal([]byte(jsonContent), &tempResult); err != nil {
			LogError("传统模式JSON解析失败: %v, 内容: %s", err, content)
			return nil, err
		}
		return &AnalysisResult{
			StudentAnswer:  ocrResult, // 传统模式使用OCR结果作为学生答案
			Score:          tempResult.Score,
			GradingDetails: tempResult.GradingDetails,
		}, nil

	case ThinkingMode, StandardMode, EconomyMode:
		// 深度思考/标准/经济模式：支持结构化输出
		var result AnalysisResult
		if err := json.Unmarshal([]byte(content), &result); err != nil {
			LogError("标准/经济模式JSON解析失败: %v, 内容: %s", err, content)
			return nil, err
		}
		return &result, nil

	default:
		return nil, fmt.Errorf("不支持的分析模式: %s", mode)
	}
}

// buildAnalysisResponse 构建并发送响应
func buildAnalysisResponse(c *gin.Context, req *AnalysisRequest, result *InternalAnalysisResult, balance int) {
	content := *result.AnalysisResponse.Choices[0].Message.Content.StringValue
	// 解析结构化分析结果
	analysisData, err := parseStructuredAnalysisResult(content, result.Mode, result.OcrResult)
	if err != nil {
		LogError("解析结构化分析结果失败: %v", err)
		// 如果解析失败，仍然返回原始内容
		analysisData = nil
	}
	// 提取需要的字段
	simplifiedResponse := SimplifiedResponse{
		ID:       result.AnalysisResponse.ID,
		Analysis: analysisData, // 结构化分析结果
		Balance:  balance,
	}
	// 返回响应
	c.JSON(http.StatusOK, simplifiedResponse)
}

// AnalysisHandler 处理分析请求
// 用户模式或默认模式下添加OpenAPI路由
// @Summary AI图像分析
// @Description 使用AI模型分析图像内容
// @Tags AI服务
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} object "分析结果"
// @Failure 400 {object} SwaggerErrorResponse "请求参数错误"
// @Failure 401 {object} SwaggerErrorResponse "未授权"
// @Router /api/v2/chat/analysis [post]
func AnalysisHandler(c *gin.Context) {
	// 验证请求
	req, ok := validateAnalysisRequest(c)
	if !ok {
		return
	}
	// 根据模式执行分析
	result, ok := performAnalysisByMode(c, req)
	if !ok {
		return
	}
	// 计算成本并记录消费
	balance, err := calculateCostAndConsume(req, result)
	if err != nil {
		LogError("计算成本失败: %v", err)
	}
	// 构建并发送响应
	buildAnalysisResponse(c, req, result, balance)
}
