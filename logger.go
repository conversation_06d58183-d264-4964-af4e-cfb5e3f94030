package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/iamxvbaba/openobserve"
)

// LogLevel 定义日志级别
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
)

// Logger 封装日志发送功能
type Logger struct {
	openObLog *openobserve.OpenObLog
	appName   string
	jobName   string
	source    string
	ctx       context.Context
	cancel    context.CancelFunc
}

var (
	loggerInstance *Logger
	loggerOnce     sync.Once
)

// NewLogger 创建新的日志实例
func NewLogger() *Logger {
	loggerOnce.Do(func() {
		logServerURL := os.Getenv("LOG_SERVER_URL")
		logServerToken := os.Getenv("LOG_SERVER_TOKEN")
		if logServerURL == "" {
			logServerURL = "http://localhost:5080" // 默认值
		}
		ctx, cancel := context.WithCancel(context.Background())
		var openObLog *openobserve.OpenObLog
		if logServerURL != "" && logServerToken != "" {
			openObLog = openobserve.New(ctx, logServerURL,
				openobserve.WithFullSize(100),
				openobserve.WithCompress(true),
				openobserve.WithWaitTime(time.Second*30),
				openobserve.WithRequestTimeout(time.Second*5),
				openobserve.WithIndexName("aig", true),
				openobserve.WithAuthorization(logServerToken))
		}
		loggerInstance = &Logger{
			openObLog: openObLog,
			appName:   "aig-function",
			jobName:   "scf-push",
			source:    "serverless-aig",
			ctx:       ctx,
			cancel:    cancel,
		}
	})
	return loggerInstance
}

// sendToOpenObserve 发送日志到OpenObserve服务器
func (l *Logger) sendToOpenObserve(level LogLevel, message string) {
	if l.openObLog == nil {
		log.Printf("[%s] %s", level, message)
		return
	}
	l.openObLog.Send(map[string]any{
		"_timestamp": time.Now().UnixMicro(),
		"level":      string(level),
		"job":        l.jobName,
		"app":        l.appName,
		"source":     l.source,
		"log":        message,
	})
}

// Debug 发送调试级别日志
func (l *Logger) Debug(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToOpenObserve(LogLevelDebug, message)
}

// Info 发送信息级别日志
func (l *Logger) Info(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToOpenObserve(LogLevelInfo, message)
}

// Warn 发送警告级别日志
func (l *Logger) Warn(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToOpenObserve(LogLevelWarn, message)
}

// Error 发送错误级别日志
func (l *Logger) Error(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToOpenObserve(LogLevelError, message)
}

// 全局日志实例
var globalLogger = NewLogger()

// 全局日志函数，方便直接调用
func LogDebug(format string, args ...any) {
	globalLogger.Debug(format, args...)
}

func LogInfo(format string, args ...any) {
	globalLogger.Info(format, args...)
}

func LogWarn(format string, args ...any) {
	globalLogger.Warn(format, args...)
}

func LogError(format string, args ...any) {
	globalLogger.Error(format, args...)
}

// LogPrint 兼容原有的log.Print调用
func LogPrint(args ...any) {
	message := fmt.Sprint(args...)
	globalLogger.Info("%s", message)
}

// LogPrintf 兼容原有的log.Printf调用
func LogPrintf(format string, args ...any) {
	globalLogger.Info(format, args...)
}

// LogPrintln 兼容原有的log.Println调用
func LogPrintln(args ...any) {
	message := fmt.Sprintln(args...)
	globalLogger.Info("%s", message)
}

// CleanupLogger 清理日志资源，确保缓存区数据能发送完成
func CleanupLogger() {
	if globalLogger != nil && globalLogger.cancel != nil {
		globalLogger.cancel()
		time.Sleep(3 * time.Second)
	}
}
