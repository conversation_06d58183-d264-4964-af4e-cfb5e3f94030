# 数据库修改任务列表

## 1. `database_operations.go` 和 `recharge_handlers.go`

* 这两个文件很可能处理充值逻辑, 需要进行以下修改以适配 `recharge_requests` 表:

* [X] 更新 `RechargeRequest` 结构体:
  * 修改字段以匹配新的表列: `ID` (int), `UserID` (uuid.UUID), `Amount` (float64), `PointsToGrant` (int), `OrderID` (string), `Status` (string), `PaymentMethod` (string), `PaymentProof` (sql.NullString), `AdminNote` (sql.NullString), `ProcessedBy` (uuid.NullUUID), `ProcessedAt` (sql.NullTime), `CreatedAt`, `UpdatedAt`.
* [X] 修改 `CreateRechargeRequest` 方法:
  * 调整 `INSERT` 语句, 确保插入 `user_id`, `amount`, `points_to_grant`, `order_id` 等新字段。
  * 移除旧表中的字段。
* [X] 新增 `transactions` 表的记录:
  * 在充值请求被批准 (`approved`) 后, 需要在 `transactions` 表中创建一条新的交易记录。
  * `source` 应为 `'RECHARGE'`。
  * `paid_points_change` 应为正值 (`points_to_grant`)。
  * `free_points_change` 应为 0。
  * 需要关联 `related_recharge_id`。

## 2. `wallet_handlers.go` 和 `user.go`

这些文件负责管理用户钱包和积分, 需适配 `wallets` 和 `transactions` 表:

* [X] 更新 `Wallet` 结构体:
  * 修改字段以匹配 `wallets` 表: `ID` (int), `UserID` (uuid.UUID), `PaidBalance` (int64), `FreeBalance` (int64), `CreatedAt`, `UpdatedAt`.
* [ ] 修改 `GetWalletByUserID` 方法:
  * 更新 `SELECT` 语句, 从 `wallets` 表中按 `user_id` 查询 `paid_balance` 和 `free_balance`。
* [ ] 修改/重构积分更新逻辑 (关键):
  * 停止直接 `UPDATE wallets`: 所有对钱包余额的修改 必须 通过在 `transactions` 表中插入一条新记录来完成。
  * 创建 `CreateTransaction` 函数: 这是核心。此函数应接收 `userID`, `source`, `paidPointsChange`, `freePointsChange`, `description` 等参数。
  * 事务化处理: `CreateTransaction` 函数内部需要在一个数据库事务中完成以下操作:
    1. 锁定用户的 `wallets` 行 (`SELECT ... FOR UPDATE`) 以防止并发问题。
    2. 计算新的 `paid_balance` 和 `free_balance`。
    3. 在 `transactions` 表中插入详细的交易记录, 包括交易前后的余额快照。
    4. 更新 `wallets` 表中用户的 `paid_balance` 和 `free_balance`。
* [ ] 适配所有消费积分的业务点:
  * 找到所有之前直接扣减积分的代码 (例如, AI服务调用)。
  * 将其替换为调用新的 `CreateTransaction` 函数, `source` 设置为 `'CONSUMPTION'`。
  * 实现优先消耗赠送积分 (`free_balance`) 的策略。

## 3. `auth.go`

用户认证和注册逻辑可能涉及初始化钱包:

* [ ] 修改用户注册/创建逻辑:
  * 当一个新用户在 `auth.users` 表中创建后, 需要在 `wallets` 表中为其创建一条对应的记录。
  * 如果存在注册赠送积分的业务, 此时应调用 `CreateTransaction` 函数, `source` 设置为 `'GIFT_SIGNUP'`。

## 4. `config.go` 和 `prompt_schema.json`

这些文件可能与 `website_configs` 和 `system_prompts` 表相关:

* [ ] 评估并实现 `system_prompts` 表的增删改查方法:
  * 如果项目需要动态管理系统提示词, 则需在 `database_operations.go` 中添加相应的方法。
* [ ] 评估并实现 `website_configs` 表的增删改查方法:
  * 如果项目需要支持多站点或动态配置, 则需添加相应的方法。

## 5. 新增功能: 财务管理

新的 `expenses` 表引入了支出管理功能, 如果需要, 可以创建新的 Go 文件 (例如 `finance_handlers.go`) 来实现:

* [ ] 创建 `Expense` 结构体。
* [ ] 实现 `CreateExpense` 方法。
* [ ] 实现 `ListExpenses` 方法 (可按分类、日期范围过滤)。
