# GEMINI 项目分析: serverless-aig

## 项目概述

`serverless-aig` 是一个基于 Go 语言的无服务器后端服务，专为部署在腾讯云云函数（SCF）上而设计。它是一个功能全面的后端系统，提供用户身份验证、钱包与交易管理、AI 模型集成以及管理功能。该项目架构分为两个主要服务：一个`面向用户的服务`和一个`管理员服务`，这种分离式部署和配置方式可以增强安全性和可维护性。

### 核心技术

*   **后端:** Go
*   **云服务商:** 腾讯云 SCF
*   **身份验证:** Supabase
*   **数据库:** PostgreSQL
*   **AI 集成:** 火山引擎（Volcengine），用于图文识别及其他 AI 模型
*   **部署:** Serverless Framework

### 主要功能

*   **用户管理:** 支持邮箱/密码注册登录、邮箱验证码登录以及密码重置功能。
*   **钱包系统:** 用户拥有包含付费和免费余额的钱包。系统支持余额查询、充值（通过充值请求）和消费。新用户会获得注册奖励。
*   **交易管理:** 所有财务活动，包括充值、消费和赠送，都会被详细记录。
*   **AI 服务:** 提供一个与 OpenAI 兼容的 API，用于与火山引擎的 AI 模型进行交互，特别是用于图像分析等任务。
*   **管理面板:** 为管理员提供一套独立的 API，用于管理系统，包括：
    *   处理用户充值请求（批准/拒绝）。
    *   手动调整用户余额。
    *   查看交易记录和财务统计数据。
*   **云存储集成:** 获取腾讯云对象存储（TOS）的临时凭证。

## 构建与运行

该项目可以在本地运行以进行测试，也可以部署到腾讯云。

### 本地开发

要在本地运行服务，您可以使用标志指定服务模式（`user` 或 `admin`）。建议使用 `.env` 文件来管理环境变量。

**先决条件:**

*   Go (版本 1.24.5 或更高)
*   一个正在运行的 PostgreSQL 数据库。
*   一个包含必要凭证的 `.env` 文件（请参阅下面的“配置”部分）。

**运行服务:**

```bash
# 以用户服务模式运行
go run . -user

# 以管理员服务模式运行
go run . -admin

# 使用 .env 文件中的环境变量运行
go run . -e
```

### 部署

通过 `serverless.yml` 文件和 Serverless Framework 来管理到腾讯云的部署。

**先决条件:**

*   Node.js 和 npm
*   Serverless Framework (`npm install -g serverless`)
*   为 Serverless Framework 配置的腾讯云凭证。

**部署命令:**

```bash
# 部署用户和管理员服务
serverless deploy

# 仅部署用户服务
serverless deploy --target user-service

# 仅部署管理员服务
serverless deploy --target admin-service
```

在部署之前，需要为 Linux 环境编译 Go 二进制文件：

```bash
# 在 Linux/macOS 上
GOOS=linux GOARCH=amd64 go build -o main .
zip main.zip main scf_bootstrap

# 在 Windows 上
set GOOS=linux
set GOARCH=amd64
go build -o main .
# 然后压缩 main.exe 和 scf_bootstrap
```

## 配置

该服务通过环境变量进行配置。在本地开发时，可以将这些变量放在 `.env` 文件中。

*   **Supabase:** `SUPABASE_URL`, `SUPABASE_KEY`, `SUPABASE_ID`
*   **PostgreSQL 数据库:** `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`
*   **火山引擎 AI:** `ARK_API_KEY`
*   **火山引擎 STS (用于 TOS):** `VOLC_STS_URL`, `VOLC_ACCESS_KEY`, `VOLC_SECRET_KEY`, `VOLC_ROLE_ARN`, `VOLC_BUCKET`, `VOLC_ENDPOINT`, `VOLC_INTRANET_ENDPOINT`, `VOLC_REGION`
*   **SMTP (用于邮件通知):** `SMTP_HOST`, `SMTP_PORT`, `SMTP_USER`, `SMTP_PASSWORD`, `SMTP_FROM`
*   **管理员:** `ADMIN_IDS` (以逗号分隔的管理员用户 UUID 列表)
*   **服务模式:** `FUNCTION_TYPE` (`user` 或 `admin`)

## 开发规范

*   **数据库模式:** 数据库模式在 `sql/postgresql_table.sql` 中定义。它包括用户、钱包、交易、充值请求和系统提示的表。该模式利用自定义的 `ENUM` 类型来表示状态和来源，并且交易被设计为原子操作。
*   **数据库操作:** 所有数据库交互都集中在 `database_operations.go` 中。代码大量使用通用表表达式（CTE）在单个数据库查询中执行复杂的原子操作，这是确保金融系统数据完整性的最佳实践。
*   **身份验证:** 身份验证由 `auth.go` 处理，它是 Supabase Go 客户端的包装器。它为保护路由和处理所有与身份验证相关的流程提供中间件。
*   **错误处理:** 代码遵循标准的 Go 错误处理实践。定义了特定的错误，如 `ErrInsufficientBalance`，以实现清晰的控制流。
*   **模块化:** 代码按功能组织（例如，`wallet_handlers.go`、`recharge_handlers.go`、`finance_handlers.go`），促进了关注点分离。
*   **系统提示:** 项目使用 `prompts` 目录来存储文本文件，这些文件很可能用作 AI 模型的系统提示。该目录中的 `readme.md` 文件列出了可用的提示。