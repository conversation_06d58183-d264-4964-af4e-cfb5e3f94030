-- 开启一个新的数据库事务
BEGIN;

-- 添加UUID扩展 (pgcrypto更现代，但uuid-ossp也可用)
-- 建议使用 pgcrypto 的 gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "pgcrypto"; 
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ====================================================================
-- 创建自定义类型 (使用 DO 块保证幂等性)
-- ====================================================================

-- 充值状态枚举类型
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'approval_status') THEN
        CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected');
    END IF;
END$$;

-- 交易类型枚举
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'transaction_source') THEN
        CREATE TYPE transaction_source AS ENUM (
            'RECHARGE',         -- 付费充值
            'CONSUMPTION',      -- 业务消费
            'GIFT_SIGNUP',      -- 注册赠送
            'GIFT_PROMO',       -- 活动赠送
            'REFUND'            -- 退款
        );
    END IF;
END$$;

-- 支出类别枚举类型
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expense_category') THEN
        CREATE TYPE expense_category AS ENUM (
            'SERVER_HOSTING',   -- 服务器/托管费
            'DOMAIN',           -- 域名费
            'API_SERVICES',     -- 第三方API调用费
            'SOFTWARE_LICENSE', -- 软件许可证
            'MARKETING_ADS',    -- 市场营销与广告
            'SALARIES_WAGES',   -- 工资薪金
            'OFFICE_SUPPLIES',  -- 办公用品
            'OTHER'             -- 其他
        );
    END IF;
END$$;


-- ====================================================================
-- 创建表结构
-- ====================================================================

-- 网站配置表
CREATE TABLE IF NOT EXISTS website_configs (
    id TEXT PRIMARY KEY,            -- 网站业务ID (如'dongni100')
    name TEXT NOT NULL,             -- 网站名称
    url TEXT NOT NULL,              -- 网站URL
    actions JSONB NOT NULL          -- 操作步骤数组 (JSON格式)
);

-- 系统提示词表
CREATE TABLE IF NOT EXISTS system_prompts (
    id SERIAL PRIMARY KEY,
    prompt_key TEXT NOT NULL UNIQUE,
    prompt_text TEXT NOT NULL,
    description TEXT,
    category TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- 充值申请表: recharge_requests
CREATE TABLE IF NOT EXISTS recharge_requests (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,          -- 对应于Supabase中的auth.users.id
    amount NUMERIC(10, 2) NOT NULL, -- 充值金额，单位：元
    points_to_grant INTEGER NOT NULL, -- 根据充值金额换算出的积分
    order_id TEXT NOT NULL UNIQUE,  -- 支付宝订单号，唯一键防止重复充值
    status approval_status NOT NULL DEFAULT 'pending',
    payment_method TEXT NOT NULL DEFAULT 'alipay'::text,
    payment_proof TEXT,             -- 支付凭证URL或路径
    admin_note TEXT,                -- 管理员处理备注
    processed_by UUID,              -- 处理此申请的管理员ID (可以为NULL，因为初始状态未处理)
    processed_at TIMESTAMPTZ,       -- 处理时间
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_recharge_requests_user_id ON recharge_requests (user_id);
CREATE INDEX IF NOT EXISTS idx_recharge_requests_status ON recharge_requests (status);

-- 钱包表: wallets
CREATE TABLE IF NOT EXISTS wallets (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL UNIQUE,   -- 对应于Supabase中的auth.users.id
    paid_balance BIGINT NOT NULL DEFAULT 0, -- 付费积分余额
    free_balance BIGINT NOT NULL DEFAULT 0, -- 赠送积分余额
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
-- 索引已通过 UNIQUE 约束隐式创建，但显式添加也无害
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets (user_id);

-- 交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL,  -- 对应于Supabase中的auth.users.id
    source transaction_source NOT NULL, -- 交易来源/类型
    -- 使用两个字段精确追踪每种积分的变化量
    -- 正数表示增加, 负数表示减少
    paid_points_change BIGINT NOT NULL DEFAULT 0,
    free_points_change BIGINT NOT NULL DEFAULT 0,
    -- 交易后的快照，用于快速对账和审计
    paid_balance_after BIGINT NOT NULL,
    free_balance_after BIGINT NOT NULL,
    description TEXT, -- 交易描述, e.g., "AI阅卷, 消耗300积分"
    -- 关联外部实体，增强可追溯性
    -- 关联充值申请
    related_recharge_id INTEGER REFERENCES recharge_requests (id) ON DELETE SET NULL,
    -- 关联的业务ID, e.g., 某次阅卷任务的ID
    related_consumption_id TEXT,
    metadata JSONB,  -- 存储额外信息
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id_created_at ON transactions (user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_source ON transactions (source);
CREATE INDEX IF NOT EXISTS idx_transactions_related_recharge_id ON transactions (related_recharge_id);

-- 支出表
CREATE TABLE IF NOT EXISTS expenses (
    id BIGSERIAL PRIMARY KEY,
    description TEXT NOT NULL,  -- 支出描述，例如 "阿里云ECS服务器 2核4G 一年"
    amount BIGINT NOT NULL,         -- 支付金额，单位：分
    currency VARCHAR(3) NOT NULL DEFAULT 'CNY',  -- 货币种类，ISO 4217代码
    category expense_category NOT NULL,  -- 支出类别
    vendor TEXT,  -- 供应商/收款方，例如 "阿里云", "GoDaddy"
    transaction_date DATE NOT NULL,      -- 实际支付日期
    service_period_start DATE NOT NULL,  -- 本次支付覆盖的服务周期的开始日期
    service_period_end DATE NOT NULL,    -- 本次支付覆盖的服务周期的结束日期
    payment_method TEXT,  -- 支付方式，例如 "alipay", "银行转账"
    invoice_url TEXT,     -- 发票或凭证的URL链接
    recorded_by UUID,     -- 记录此条支出的管理员用户ID (可选)
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses (category);
CREATE INDEX IF NOT EXISTS idx_expenses_period ON expenses (service_period_start, service_period_end);

-- 提交事务
COMMIT;