package main

// LoginRequest 表示登录请求的结构
// @Description 用户登录请求
type SwaggerLoginRequest struct {
	Email    string `json:"email" example:"<EMAIL>" binding:"required,email"`
	Password string `json:"password" example:"password123" binding:"required,min=6"`
}

// LoginResponse 表示登录响应的结构
// @Description 用户登录响应
type SwaggerLoginResponse struct {
	AccessToken  string       `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string       `json:"refresh_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	ExpiresIn    int          `json:"expires_in" example:"3600"`
	ExpiresAt    int64        `json:"expires_at" example:"1625097700"`
	Config       []ConfigItem `json:"config"`
	Subjects     []Subject    `json:"subjects"`
}

// RegisterRequest 表示注册请求的结构
// @Description 用户注册请求
type SwaggerRegisterRequest struct {
	Email    string `json:"email" example:"<EMAIL>" binding:"required,email"`
	Password string `json:"password" example:"password123" binding:"required,min=6"`
}

// RegisterResponse 表示注册响应的结构
// @Description 用户注册响应
type SwaggerRegisterResponse struct {
	Code    int    `json:"code" example:"200"`
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"注册成功"`
}

// RecoverRequest 表示密码恢复请求的结构
// @Description 密码恢复请求
type SwaggerRecoverRequest struct {
	Email string `json:"email" example:"<EMAIL>" binding:"required,email"`
}

// RecoverResponse 表示密码恢复响应的结构
// @Description 密码恢复响应
type SwaggerRecoverResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"密码重置邮件已发送"`
}

// VerifyRequest 表示验证令牌请求的结构
// @Description 验证令牌请求
type SwaggerVerifyRequest struct {
	Email string `json:"email" example:"<EMAIL>" binding:"required,email"`
	Token string `json:"token" example:"123456" binding:"required"`
	Type  string `json:"type" example:"recovery" binding:"required"`
}

// VerifyResponse 表示验证令牌响应的结构
// @Description 验证令牌响应
type SwaggerVerifyResponse struct {
	Success      bool   `json:"success" example:"true"`
	Message      string `json:"message,omitempty" example:"验证成功"`
	AccessToken  string `json:"access_token,omitempty" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string `json:"refresh_token,omitempty" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	ExpiresIn    int    `json:"expires_in,omitempty" example:"3600"`
	ExpiresAt    int64  `json:"expires_at,omitempty" example:"1625097700"`
}

// UpdatePasswordRequest 表示更新密码请求的结构
// @Description 更新密码请求
type SwaggerUpdatePasswordRequest struct {
	Password string `json:"password" example:"newpassword123" binding:"required,min=6"`
}

// UpdatePasswordResponse 表示更新密码响应的结构
// @Description 更新密码响应
type SwaggerUpdatePasswordResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"密码更新成功"`
}

// EmailCodeRequest 表示发送邮箱验证码请求的结构
// @Description 发送邮箱验证码请求
type SwaggerEmailCodeRequest struct {
	Email string `json:"email" example:"<EMAIL>" binding:"required,email"`
}

// EmailCodeResponse 表示发送邮箱验证码响应的结构
// @Description 发送邮箱验证码响应
type SwaggerEmailCodeResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"验证码已发送到您的邮箱"`
}

// EmailLoginRequest 表示邮箱验证码登录请求的结构
// @Description 邮箱验证码登录请求
type SwaggerEmailLoginRequest struct {
	Email string `json:"email" example:"<EMAIL>" binding:"required,email"`
	Code  string `json:"code" example:"123456" binding:"required"`
}

// AppVersionConfig 表示应用版本信息
// @Description 应用版本信息
type SwaggerAppVersionConfig struct {
	Version     string `json:"version" example:"v1.0.0"`
	DownloadURL string `json:"download_url" example:"https://example.com/download"`
	ForceUpdate bool   `json:"force_update" example:"false"`
	UpdateLog   string `json:"update_log" example:"修复了一些问题"`
}

// ErrorResponse 表示错误响应
// @Description 错误响应
type SwaggerErrorResponse struct {
	Error   string `json:"error" example:"Invalid request"`
	Message string `json:"message,omitempty" example:"请求参数错误"`
}

// SuccessResponse 表示成功响应
// @Description 成功响应
type SwaggerSuccessResponse struct {
	Message string `json:"message" example:"操作成功"`
	User    any    `json:"user,omitempty"`
}
